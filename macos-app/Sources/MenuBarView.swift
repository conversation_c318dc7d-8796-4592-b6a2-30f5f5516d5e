import SwiftUI
import UserNotifications

struct MenuBarView: View {
    @EnvironmentObject var appState: AppState
    @Environment(\.openWindow) private var openWindow
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header with connection status
            MenuBarHeader()
            
            Divider()
            
            // Quick stats
            MenuBarQuickStats()
            
            Divider()
            
            // Services section
            MenuBarServicesSection()
            
            Divider()
            
            // Docker section
            MenuBarDockerSection()
            
            Divider()
            
            // Quick actions
            MenuBarQuickActions()
            
            Divider()
            
            // App controls
            MenuBarAppControls()
        }
        .frame(width: 280)
        .background(Color(NSColor.controlBackgroundColor))
    }
}

// MARK: - Header

struct MenuBarHeader: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        HStack {
            Image(systemName: "server.rack")
                .foregroundColor(.accentColor)
            
            VStack(alignment: .leading, spacing: 2) {
                Text("WOW Monitor")
                    .font(.headline)
                    .fontWeight(.medium)
                
                HStack(spacing: 4) {
                    Circle()
                        .fill(appState.isConnected ? Color.green : Color.red)
                        .frame(width: 6, height: 6)
                    
                    Text(appState.isConnected ? "Connected" : "Disconnected")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            Button(action: {
                appState.refreshData()
            }) {
                Image(systemName: "arrow.clockwise")
                    .font(.caption)
            }
            .buttonStyle(.plain)
            .help("Refresh Data")
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
    }
}

// MARK: - Quick Stats

struct MenuBarQuickStats: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        VStack(spacing: 6) {
            HStack {
                StatItem(
                    icon: "checkmark.circle.fill",
                    color: .green,
                    value: "\(appState.services.filter { $0.status == .running }.count)",
                    label: "Running"
                )
                
                Spacer()
                
                StatItem(
                    icon: "xmark.circle.fill",
                    color: .red,
                    value: "\(appState.services.filter { $0.status == .stopped }.count)",
                    label: "Stopped"
                )
                
                Spacer()
                
                StatItem(
                    icon: "cube.fill",
                    color: .blue,
                    value: "\(appState.dockerContainers.filter { $0.state == "running" }.count)",
                    label: "Containers"
                )
            }
            
            if let metrics = appState.systemMetrics {
                HStack {
                    StatItem(
                        icon: "cpu.fill",
                        color: cpuColor(metrics.cpu.usage),
                        value: "\(Int(metrics.cpu.usage))%",
                        label: "CPU"
                    )
                    
                    Spacer()
                    
                    StatItem(
                        icon: "memorychip.fill",
                        color: memoryColor(metrics.memory.usedPercentage),
                        value: "\(Int(metrics.memory.usedPercentage))%",
                        label: "Memory"
                    )
                    
                    Spacer()
                    
                    StatItem(
                        icon: "internaldrive.fill",
                        color: diskColor(metrics.disk.usedPercentage),
                        value: "\(Int(metrics.disk.usedPercentage))%",
                        label: "Disk"
                    )
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
    }
    
    private func cpuColor(_ usage: Double) -> Color {
        if usage > 80 { return .red }
        if usage > 60 { return .orange }
        return .green
    }
    
    private func memoryColor(_ usage: Double) -> Color {
        if usage > 85 { return .red }
        if usage > 70 { return .orange }
        return .green
    }
    
    private func diskColor(_ usage: Double) -> Color {
        if usage > 90 { return .red }
        if usage > 75 { return .orange }
        return .green
    }
}

struct StatItem: View {
    let icon: String
    let color: Color
    let value: String
    let label: String
    
    var body: some View {
        VStack(spacing: 2) {
            HStack(spacing: 3) {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.caption2)
                
                Text(value)
                    .font(.caption)
                    .fontWeight(.medium)
                    .monospacedDigit()
            }
            
            Text(label)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - Services Section

struct MenuBarServicesSection: View {
    @EnvironmentObject var appState: AppState
    @Environment(\.openWindow) private var openWindow
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text("Services")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Button("View All") {
                    openWindow(id: "main")
                }
                .font(.caption2)
                .buttonStyle(.plain)
                .foregroundColor(.accentColor)
            }
            .padding(.horizontal, 12)
            
            if appState.services.isEmpty {
                Text("No services available")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 12)
            } else {
                LazyVStack(spacing: 2) {
                    ForEach(Array(appState.services.prefix(5)), id: \.id) { service in
                        MenuBarServiceRow(service: service)
                    }
                    
                    if appState.services.count > 5 {
                        HStack {
                            Text("and \(appState.services.count - 5) more...")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                            Spacer()
                        }
                        .padding(.horizontal, 12)
                        .padding(.top, 2)
                    }
                }
            }
        }
        .padding(.vertical, 6)
    }
}

struct MenuBarServiceRow: View {
    let service: ServiceInfo
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        HStack(spacing: 8) {
            // Status indicator
            Circle()
                .fill(service.status.color)
                .frame(width: 6, height: 6)
            
            // Service name
            Text(service.name)
                .font(.caption)
                .lineLimit(1)
            
            Spacer()
            
            // Quick action button
            Button(action: {
                toggleService()
            }) {
                Image(systemName: service.status == .running ? "stop.fill" : "play.fill")
                    .font(.caption2)
                    .foregroundColor(service.status == .running ? .red : .green)
            }
            .buttonStyle(.plain)
            .help(service.status == .running ? "Stop Service" : "Start Service")
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 2)
        .contentShape(Rectangle())
        .onTapGesture {
            // Open service details
        }
    }
    
    private func toggleService() {
        Task {
            if service.status == .running {
                await appState.stopService(service.name)
            } else {
                await appState.startService(service.name)
            }
        }
    }
}

// MARK: - Docker Section

struct MenuBarDockerSection: View {
    @EnvironmentObject var appState: AppState
    @Environment(\.openWindow) private var openWindow
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text("Docker")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Button("View All") {
                    openWindow(id: "main")
                }
                .font(.caption2)
                .buttonStyle(.plain)
                .foregroundColor(.accentColor)
            }
            .padding(.horizontal, 12)
            
            if appState.dockerContainers.isEmpty {
                Text("No containers available")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 12)
            } else {
                LazyVStack(spacing: 2) {
                    ForEach(Array(appState.dockerContainers.prefix(3)), id: \.id) { container in
                        MenuBarContainerRow(container: container)
                    }
                    
                    if appState.dockerContainers.count > 3 {
                        HStack {
                            Text("and \(appState.dockerContainers.count - 3) more...")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                            Spacer()
                        }
                        .padding(.horizontal, 12)
                        .padding(.top, 2)
                    }
                }
            }
        }
        .padding(.vertical, 6)
    }
}

struct MenuBarContainerRow: View {
    let container: DockerContainer
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        HStack(spacing: 8) {
            // Status indicator
            Circle()
                .fill(container.state == "running" ? Color.green : Color.gray)
                .frame(width: 6, height: 6)
            
            // Container name
            Text(container.name)
                .font(.caption)
                .lineLimit(1)
            
            Spacer()
            
            // Quick action button
            Button(action: {
                toggleContainer()
            }) {
                Image(systemName: container.state == "running" ? "stop.fill" : "play.fill")
                    .font(.caption2)
                    .foregroundColor(container.state == "running" ? .red : .green)
            }
            .buttonStyle(.plain)
            .help(container.state == "running" ? "Stop Container" : "Start Container")
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 2)
        .contentShape(Rectangle())
        .onTapGesture {
            // Open container details
        }
    }
    
    private func toggleContainer() {
        Task {
            if container.state == "running" {
                await appState.stopContainer(container.id)
            } else {
                await appState.startContainer(container.id)
            }
        }
    }
}

// MARK: - Quick Actions

struct MenuBarQuickActions: View {
    @EnvironmentObject var appState: AppState
    @Environment(\.openWindow) private var openWindow
    
    var body: some View {
        VStack(spacing: 6) {
            Text("Quick Actions")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.horizontal, 12)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 8) {
                QuickActionButton(
                    icon: "arrow.clockwise",
                    title: "Refresh All",
                    action: {
                        appState.refreshData()
                    }
                )
                
                QuickActionButton(
                    icon: "play.fill",
                    title: "Start All",
                    action: {
                        Task {
                            await appState.startAllServices()
                        }
                    }
                )
                
                QuickActionButton(
                    icon: "stop.fill",
                    title: "Stop All",
                    action: {
                        Task {
                            await appState.stopAllServices()
                        }
                    }
                )
                
                QuickActionButton(
                    icon: "chart.bar.fill",
                    title: "Metrics",
                    action: {
                        openWindow(id: "main")
                    }
                )
            }
            .padding(.horizontal, 12)
        }
        .padding(.vertical, 6)
    }
}

struct QuickActionButton: View {
    let icon: String
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.caption)
                    .foregroundColor(.accentColor)
                
                Text(title)
                    .font(.caption2)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 8)
            .background(Color(NSColor.controlColor))
            .cornerRadius(6)
        }
        .buttonStyle(.plain)
    }
}

// MARK: - App Controls

struct MenuBarAppControls: View {
    @Environment(\.openWindow) private var openWindow
    
    var body: some View {
        VStack(spacing: 4) {
            Button("Open Main Window") {
                openWindow(id: "main")
            }
            .buttonStyle(.plain)
            .foregroundColor(.accentColor)
            .frame(maxWidth: .infinity, alignment: .leading)
            
            Button("Settings...") {
                openWindow(id: "settings")
            }
            .buttonStyle(.plain)
            .foregroundColor(.accentColor)
            .frame(maxWidth: .infinity, alignment: .leading)
            
            Divider()
            
            Button("Quit WOW Monitor") {
                NSApplication.shared.terminate(nil)
            }
            .buttonStyle(.plain)
            .foregroundColor(.primary)
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .font(.caption)
    }
}

// MARK: - Extensions

extension ServiceStatus {
    var color: Color {
        switch self {
        case .running:
            return .green
        case .stopped:
            return .red
        case .starting:
            return .orange
        case .stopping:
            return .orange
        case .error:
            return .red
        case .unknown:
            return .gray
        }
    }
}

extension AppState {
    func startAllServices() async {
        for service in services.filter({ $0.status != .running }) {
            await startService(service.name)
        }
    }
    
    func stopAllServices() async {
        for service in services.filter({ $0.status == .running }) {
            await stopService(service.name)
        }
    }
    
    func startContainer(_ id: String) async {
        // Implementation for starting Docker container
    }
    
    func stopContainer(_ id: String) async {
        // Implementation for stopping Docker container
    }
}

#Preview {
    MenuBarView()
        .environmentObject(AppState())
}