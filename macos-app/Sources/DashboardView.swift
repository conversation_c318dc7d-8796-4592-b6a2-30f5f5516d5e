import SwiftUI
import Charts

struct DashboardView: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: gridColumns, spacing: 20) {
                // System Status Card
                SystemStatusCard()
                
                // Services Overview Card
                ServicesOverviewCard()
                
                // Docker Overview Card
                DockerOverviewCard()
                
                // System Metrics Card
                SystemMetricsCard()
                
                // Recent Notifications Card
                RecentNotificationsCard()
                
                // Quick Actions Card
                QuickActionsCard()
            }
            .padding()
        }
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    private var gridColumns: [GridItem] {
        [
            GridItem(.flexible(), spacing: 20),
            GridItem(.flexible(), spacing: 20)
        ]
    }
}

struct SystemStatusCard: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        DashboardCard(title: "System Status", icon: "server.rack") {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Image(systemName: appState.serverStatus.icon)
                        .foregroundColor(appState.serverStatus.color)
                        .font(.title2)
                    
                    VStack(alignment: .leading) {
                        Text("Backend Server")
                            .font(.headline)
                        Text(statusText)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                }
                
                if let metrics = appState.systemMetrics {
                    Divider()
                    
                    HStack {
                        MetricItem(title: "Uptime", value: formatUptime(metrics.uptime))
                        Spacer()
                        MetricItem(title: "Load", value: String(format: "%.2f", metrics.loadAverage.first ?? 0))
                    }
                }
            }
        }
    }
    
    private var statusText: String {
        switch appState.serverStatus {
        case .healthy: return "All systems operational"
        case .warning: return "Some issues detected"
        case .error: return "Critical issues"
        case .unknown: return "Status unknown"
        }
    }
    
    private func formatUptime(_ uptime: TimeInterval) -> String {
        let days = Int(uptime) / 86400
        let hours = (Int(uptime) % 86400) / 3600
        let minutes = (Int(uptime) % 3600) / 60
        
        if days > 0 {
            return "\(days)d \(hours)h"
        } else if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
}

struct ServicesOverviewCard: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        DashboardCard(title: "Services", icon: "gear") {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text("\(runningServices)/\(totalServices)")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("Running")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                }
                
                if totalServices > 0 {
                    ProgressView(value: Double(runningServices), total: Double(totalServices))
                        .progressViewStyle(LinearProgressViewStyle(tint: .green))
                    
                    HStack {
                        StatusDot(color: .green, count: runningServices, label: "Running")
                        StatusDot(color: .red, count: stoppedServices, label: "Stopped")
                        StatusDot(color: .orange, count: errorServices, label: "Error")
                    }
                } else {
                    Text("No services configured")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    private var totalServices: Int { appState.services.count }
    private var runningServices: Int { appState.services.filter { $0.status == .running }.count }
    private var stoppedServices: Int { appState.services.filter { $0.status == .stopped }.count }
    private var errorServices: Int { appState.services.filter { $0.status == .error }.count }
}

struct DockerOverviewCard: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        DashboardCard(title: "Docker Containers", icon: "shippingbox") {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text("\(runningContainers)/\(totalContainers)")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("Running")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                }
                
                if totalContainers > 0 {
                    ProgressView(value: Double(runningContainers), total: Double(totalContainers))
                        .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                    
                    HStack {
                        StatusDot(color: .green, count: runningContainers, label: "Running")
                        StatusDot(color: .red, count: exitedContainers, label: "Exited")
                        StatusDot(color: .orange, count: otherContainers, label: "Other")
                    }
                } else {
                    Text("No containers found")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    private var totalContainers: Int { appState.dockerContainers.count }
    private var runningContainers: Int { appState.dockerContainers.filter { $0.status == .running }.count }
    private var exitedContainers: Int { appState.dockerContainers.filter { $0.status == .exited }.count }
    private var otherContainers: Int { totalContainers - runningContainers - exitedContainers }
}

struct SystemMetricsCard: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        DashboardCard(title: "System Metrics", icon: "cpu") {
            if let metrics = appState.systemMetrics {
                VStack(spacing: 16) {
                    HStack {
                        CircularProgressView(
                            progress: metrics.cpu.usage / 100,
                            title: "CPU",
                            value: "\(Int(metrics.cpu.usage))%",
                            color: .blue
                        )
                        
                        CircularProgressView(
                            progress: Double(metrics.memory.used) / Double(metrics.memory.total),
                            title: "Memory",
                            value: formatBytes(metrics.memory.used),
                            color: .green
                        )
                    }
                    
                    HStack {
                        CircularProgressView(
                            progress: Double(metrics.disk.used) / Double(metrics.disk.total),
                            title: "Disk",
                            value: formatBytes(metrics.disk.used),
                            color: .orange
                        )
                        
                        VStack(alignment: .leading) {
                            Text("Network")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            HStack {
                                Image(systemName: "arrow.down")
                                    .foregroundColor(.green)
                                Text(formatBytes(metrics.network.bytesReceived))
                                    .font(.caption)
                            }
                            
                            HStack {
                                Image(systemName: "arrow.up")
                                    .foregroundColor(.blue)
                                Text(formatBytes(metrics.network.bytesSent))
                                    .font(.caption)
                            }
                        }
                        
                        Spacer()
                    }
                }
            } else {
                Text("No metrics available")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .binary
        return formatter.string(fromByteCount: bytes)
    }
}

struct RecentNotificationsCard: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        DashboardCard(title: "Recent Notifications", icon: "bell") {
            if appState.notifications.isEmpty {
                Text("No recent notifications")
                    .font(.caption)
                    .foregroundColor(.secondary)
            } else {
                VStack(alignment: .leading, spacing: 8) {
                    ForEach(Array(appState.notifications.prefix(3))) { notification in
                        HStack {
                            Image(systemName: notification.type.icon)
                                .foregroundColor(Color(notification.type.color))
                                .frame(width: 16)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text(notification.message)
                                    .font(.caption)
                                    .lineLimit(2)
                                
                                Text(notification.timestamp, style: .relative)
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                        }
                        
                        if notification.id != appState.notifications.prefix(3).last?.id {
                            Divider()
                        }
                    }
                }
            }
        }
    }
}

struct QuickActionsCard: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        DashboardCard(title: "Quick Actions", icon: "bolt") {
            VStack(spacing: 12) {
                Button(action: { appState.refreshData() }) {
                    Label("Refresh All Data", systemImage: "arrow.clockwise")
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(.borderedProminent)
                
                HStack {
                    Button(action: { restartAllServices() }) {
                        Label("Restart Services", systemImage: "arrow.clockwise.circle")
                    }
                    .buttonStyle(.bordered)
                    
                    Button(action: { viewLogs() }) {
                        Label("View Logs", systemImage: "doc.text")
                    }
                    .buttonStyle(.bordered)
                }
            }
        }
    }
    
    private func restartAllServices() {
        // Implementation for restarting all services
        Task {
            for service in appState.services {
                try? await appState.restartService(service.id)
            }
        }
    }
    
    private func viewLogs() {
        // Implementation for viewing logs
        // This would typically navigate to the logs view
    }
}

// MARK: - Supporting Views

struct DashboardCard<Content: View>: View {
    let title: String
    let icon: String
    let content: Content
    
    init(title: String, icon: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.icon = icon
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.accentColor)
                
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            content
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct MetricItem: View {
    let title: String
    let value: String
    
    var body: some View {
        VStack(alignment: .leading) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            Text(value)
                .font(.headline)
                .fontWeight(.semibold)
        }
    }
}

struct StatusDot: View {
    let color: Color
    let count: Int
    let label: String
    
    var body: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(color)
                .frame(width: 8, height: 8)
            
            Text("\(count) \(label)")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
}

struct CircularProgressView: View {
    let progress: Double
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack {
            ZStack {
                Circle()
                    .stroke(color.opacity(0.2), lineWidth: 4)
                
                Circle()
                    .trim(from: 0, to: progress)
                    .stroke(color, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut, value: progress)
                
                Text(value)
                    .font(.caption2)
                    .fontWeight(.semibold)
            }
            .frame(width: 50, height: 50)
            
            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
}

#Preview {
    DashboardView()
        .environmentObject(AppState())
        .frame(width: 800, height: 600)
}