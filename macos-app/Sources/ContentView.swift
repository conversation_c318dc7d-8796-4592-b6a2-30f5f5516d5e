import SwiftUI

struct ContentView: View {
    @EnvironmentObject var appState: AppState
    @State private var selectedTab = 0
    @State private var showingSettings = false
    
    var body: some View {
        NavigationSplitView {
            SidebarView(selectedTab: $selectedTab)
                .navigationSplitViewColumnWidth(min: 200, ideal: 250)
        } detail: {
            Group {
                switch selectedTab {
                case 0:
                    DashboardView()
                case 1:
                    ServicesView()
                case 2:
                    DockerView()
                case 3:
                    SystemMetricsView()
                case 4:
                    LogsView()
                case 5:
                    NotificationsView()
                default:
                    DashboardView()
                }
            }
            .navigationTitle(tabTitle(for: selectedTab))
            .toolbar {
                ToolbarItemGroup(placement: .primaryAction) {
                    ConnectionStatusView()
                    
                    Button(action: { appState.refreshData() }) {
                        Image(systemName: "arrow.clockwise")
                    }
                    .help("Refresh Data")
                    
                    Button(action: { showingSettings = true }) {
                        Image(systemName: "gear")
                    }
                    .help("Settings")
                }
            }
        }
        .onAppear {
            appState.connect()
        }
        .sheet(isPresented: $showingSettings) {
            SettingsView()
                .environmentObject(appState)
        }
    }
    
    private func tabTitle(for index: Int) -> String {
        switch index {
        case 0: return "Dashboard"
        case 1: return "Services"
        case 2: return "Docker"
        case 3: return "System Metrics"
        case 4: return "Logs"
        case 5: return "Notifications"
        default: return "WOW"
        }
    }
}

struct SidebarView: View {
    @Binding var selectedTab: Int
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        List(selection: $selectedTab) {
            Section("Overview") {
                SidebarItem(icon: "chart.line.uptrend.xyaxis", title: "Dashboard", tag: 0)
                SidebarItem(icon: "server.rack", title: "Services", tag: 1, badge: servicesBadge)
                SidebarItem(icon: "shippingbox", title: "Docker", tag: 2, badge: dockerBadge)
            }
            
            Section("Monitoring") {
                SidebarItem(icon: "cpu", title: "System Metrics", tag: 3)
                SidebarItem(icon: "doc.text", title: "Logs", tag: 4)
                SidebarItem(icon: "bell", title: "Notifications", tag: 5, badge: notificationsBadge)
            }
        }
        .listStyle(SidebarListStyle())
        .navigationTitle("WOW")
    }
    
    private var servicesBadge: String? {
        let runningCount = appState.services.filter { $0.status == .running }.count
        let totalCount = appState.services.count
        return totalCount > 0 ? "\(runningCount)/\(totalCount)" : nil
    }
    
    private var dockerBadge: String? {
        let runningCount = appState.dockerContainers.filter { $0.status == .running }.count
        let totalCount = appState.dockerContainers.count
        return totalCount > 0 ? "\(runningCount)/\(totalCount)" : nil
    }
    
    private var notificationsBadge: String? {
        let count = appState.notifications.count
        return count > 0 ? "\(count)" : nil
    }
}

struct SidebarItem: View {
    let icon: String
    let title: String
    let tag: Int
    let badge: String?
    
    init(icon: String, title: String, tag: Int, badge: String? = nil) {
        self.icon = icon
        self.title = title
        self.tag = tag
        self.badge = badge
    }
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.accentColor)
                .frame(width: 20)
            
            Text(title)
            
            Spacer()
            
            if let badge = badge {
                Text(badge)
                    .font(.caption)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.accentColor)
                    .foregroundColor(.white)
                    .clipShape(Capsule())
            }
        }
        .tag(tag)
    }
}

struct ConnectionStatusView: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(appState.isConnected ? Color.green : Color.red)
                .frame(width: 8, height: 8)
            
            Text(appState.isConnected ? "Connected" : "Disconnected")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .help(appState.isConnected ? "Connected to WOW backend" : "Disconnected from WOW backend")
    }
}

#Preview {
    ContentView()
        .environmentObject(AppState())
        .frame(width: 1200, height: 800)
}