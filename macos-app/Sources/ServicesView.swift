import SwiftUI

struct ServicesView: View {
    @EnvironmentObject var appState: AppState
    @State private var searchText = ""
    @State private var selectedService: ServiceInfo?
    @State private var showingServiceDetails = false
    @State private var sortOrder: ServiceSortOrder = .name
    @State private var filterStatus: ServiceStatus? = nil
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with controls
            HeaderView()
            
            // Services list
            if filteredServices.isEmpty {
                EmptyStateView()
            } else {
                ServicesListView()
            }
        }
        .sheet(isPresented: $showingServiceDetails) {
            if let service = selectedService {
                ServiceDetailView(service: service)
            }
        }
    }
    
    private var filteredServices: [ServiceInfo] {
        var services = appState.services
        
        // Apply search filter
        if !searchText.isEmpty {
            services = services.filter { service in
                service.name.localizedCaseInsensitiveContains(searchText) ||
                service.description.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // Apply status filter
        if let filterStatus = filterStatus {
            services = services.filter { $0.status == filterStatus }
        }
        
        // Apply sorting
        switch sortOrder {
        case .name:
            services.sort { $0.name < $1.name }
        case .status:
            services.sort { $0.status.rawValue < $1.status.rawValue }
        case .lastUpdated:
            services.sort { $0.lastUpdated > $1.lastUpdated }
        }
        
        return services
    }
    
    @ViewBuilder
    private func HeaderView() -> some View {
        VStack(spacing: 12) {
            HStack {
                // Search bar
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                    
                    TextField("Search services...", text: $searchText)
                        .textFieldStyle(.plain)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 6)
                .background(Color(NSColor.controlBackgroundColor))
                .clipShape(RoundedRectangle(cornerRadius: 6))
                
                Spacer()
                
                // Filter and sort controls
                Menu {
                    Button("All Services") { filterStatus = nil }
                    Button("Running") { filterStatus = .running }
                    Button("Stopped") { filterStatus = .stopped }
                    Button("Error") { filterStatus = .error }
                } label: {
                    Label("Filter", systemImage: "line.3.horizontal.decrease.circle")
                }
                .menuStyle(.borderlessButton)
                
                Menu {
                    Button("Name") { sortOrder = .name }
                    Button("Status") { sortOrder = .status }
                    Button("Last Updated") { sortOrder = .lastUpdated }
                } label: {
                    Label("Sort", systemImage: "arrow.up.arrow.down")
                }
                .menuStyle(.borderlessButton)
                
                Button(action: { appState.refreshServices() }) {
                    Image(systemName: "arrow.clockwise")
                }
                .buttonStyle(.borderless)
            }
            
            // Status summary
            HStack {
                StatusSummaryItem(
                    title: "Total",
                    count: appState.services.count,
                    color: .primary
                )
                
                StatusSummaryItem(
                    title: "Running",
                    count: appState.services.filter { $0.status == .running }.count,
                    color: .green
                )
                
                StatusSummaryItem(
                    title: "Stopped",
                    count: appState.services.filter { $0.status == .stopped }.count,
                    color: .red
                )
                
                StatusSummaryItem(
                    title: "Error",
                    count: appState.services.filter { $0.status == .error }.count,
                    color: .orange
                )
                
                Spacer()
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    @ViewBuilder
    private func EmptyStateView() -> some View {
        VStack(spacing: 16) {
            Image(systemName: "gear")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("No Services Found")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text(searchText.isEmpty ? "No services are configured" : "No services match your search")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            if !searchText.isEmpty {
                Button("Clear Search") {
                    searchText = ""
                }
                .buttonStyle(.borderedProminent)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    @ViewBuilder
    private func ServicesListView() -> some View {
        List(filteredServices) { service in
            ServiceRowView(service: service) {
                selectedService = service
                showingServiceDetails = true
            }
            .listRowSeparator(.hidden)
            .listRowBackground(Color.clear)
        }
        .listStyle(.plain)
    }
}

struct ServiceRowView: View {
    let service: ServiceInfo
    let onTap: () -> Void
    @EnvironmentObject var appState: AppState
    @State private var isPerformingAction = false
    
    var body: some View {
        HStack(spacing: 16) {
            // Status indicator
            VStack {
                Circle()
                    .fill(service.status.color)
                    .frame(width: 12, height: 12)
                
                if service.status == .running {
                    Circle()
                        .fill(service.status.color)
                        .frame(width: 8, height: 8)
                        .scaleEffect(isPerformingAction ? 1.2 : 0.8)
                        .opacity(isPerformingAction ? 0.5 : 1.0)
                        .animation(.easeInOut(duration: 0.6).repeatForever(), value: isPerformingAction)
                }
            }
            .frame(width: 20)
            
            // Service info
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(service.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Spacer()
                    
                    Text(service.status.displayName)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(service.status.color.opacity(0.2))
                        .foregroundColor(service.status.color)
                        .clipShape(Capsule())
                }
                
                Text(service.description)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                
                HStack {
                    if let port = service.port {
                        Label("Port \(port)", systemImage: "network")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    if let version = service.version {
                        Label("v\(version)", systemImage: "tag")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Text("Updated \(service.lastUpdated, style: .relative)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Action buttons
            HStack(spacing: 8) {
                if service.status == .running {
                    Button(action: { stopService() }) {
                        Image(systemName: "stop.fill")
                    }
                    .buttonStyle(.borderless)
                    .foregroundColor(.red)
                    .disabled(isPerformingAction)
                    
                    Button(action: { restartService() }) {
                        Image(systemName: "arrow.clockwise")
                    }
                    .buttonStyle(.borderless)
                    .foregroundColor(.orange)
                    .disabled(isPerformingAction)
                } else {
                    Button(action: { startService() }) {
                        Image(systemName: "play.fill")
                    }
                    .buttonStyle(.borderless)
                    .foregroundColor(.green)
                    .disabled(isPerformingAction)
                }
                
                Button(action: onTap) {
                    Image(systemName: "info.circle")
                }
                .buttonStyle(.borderless)
                .foregroundColor(.blue)
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 8))
        .shadow(color: .black.opacity(0.1), radius: 1, x: 0, y: 1)
        .onAppear {
            if service.status == .running {
                isPerformingAction = true
            }
        }
    }
    
    private func startService() {
        isPerformingAction = true
        Task {
            try? await appState.startService(service.id)
            await MainActor.run {
                isPerformingAction = false
            }
        }
    }
    
    private func stopService() {
        isPerformingAction = true
        Task {
            try? await appState.stopService(service.id)
            await MainActor.run {
                isPerformingAction = false
            }
        }
    }
    
    private func restartService() {
        isPerformingAction = true
        Task {
            try? await appState.restartService(service.id)
            await MainActor.run {
                isPerformingAction = false
            }
        }
    }
}

struct StatusSummaryItem: View {
    let title: String
    let count: Int
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 2) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text("\(count)")
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
    }
}

struct ServiceDetailView: View {
    let service: ServiceInfo
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var appState: AppState
    @State private var logs: [String] = []
    @State private var isLoadingLogs = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Service header
                ServiceHeaderView()
                
                // Tabs
                TabView {
                    // Overview tab
                    ServiceOverviewTab()
                        .tabItem {
                            Label("Overview", systemImage: "info.circle")
                        }
                    
                    // Logs tab
                    ServiceLogsTab()
                        .tabItem {
                            Label("Logs", systemImage: "doc.text")
                        }
                    
                    // Configuration tab
                    ServiceConfigTab()
                        .tabItem {
                            Label("Config", systemImage: "gear")
                        }
                }
            }
            .navigationTitle(service.name)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Close") {
                        dismiss()
                    }
                }
            }
        }
        .frame(width: 600, height: 500)
    }
    
    @ViewBuilder
    private func ServiceHeaderView() -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Circle()
                        .fill(service.status.color)
                        .frame(width: 16, height: 16)
                    
                    Text(service.status.displayName)
                        .font(.headline)
                        .foregroundColor(service.status.color)
                }
                
                Text(service.description)
                    .font(.body)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Quick actions
            HStack(spacing: 8) {
                if service.status == .running {
                    Button("Stop") {
                        Task {
                            try? await appState.stopService(service.id)
                        }
                    }
                    .buttonStyle(.bordered)
                    
                    Button("Restart") {
                        Task {
                            try? await appState.restartService(service.id)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                } else {
                    Button("Start") {
                        Task {
                            try? await appState.startService(service.id)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                }
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    @ViewBuilder
    private func ServiceOverviewTab() -> some View {
        ScrollView {
            LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 16) {
                InfoCard(title: "Service ID", value: service.id)
                InfoCard(title: "Version", value: service.version ?? "Unknown")
                InfoCard(title: "Port", value: service.port?.description ?? "N/A")
                InfoCard(title: "Last Updated", value: service.lastUpdated.formatted())
            }
            .padding()
        }
    }
    
    @ViewBuilder
    private func ServiceLogsTab() -> some View {
        VStack {
            HStack {
                Button("Refresh Logs") {
                    loadLogs()
                }
                .buttonStyle(.bordered)
                
                Spacer()
                
                if isLoadingLogs {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            .padding()
            
            ScrollView {
                LazyVStack(alignment: .leading, spacing: 4) {
                    ForEach(logs.indices, id: \.self) { index in
                        Text(logs[index])
                            .font(.system(.caption, design: .monospaced))
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                }
                .padding()
            }
            .background(Color(NSColor.textBackgroundColor))
            .clipShape(RoundedRectangle(cornerRadius: 8))
            .padding(.horizontal)
        }
        .onAppear {
            loadLogs()
        }
    }
    
    @ViewBuilder
    private func ServiceConfigTab() -> some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                Text("Configuration")
                    .font(.headline)
                
                Text("Service configuration details would be displayed here.")
                    .foregroundColor(.secondary)
                
                // Add more configuration details as needed
            }
            .padding()
        }
    }
    
    private func loadLogs() {
        isLoadingLogs = true
        Task {
            do {
                let serviceLogs = try await appState.getServiceLogs(service.id)
                await MainActor.run {
                    self.logs = serviceLogs
                    self.isLoadingLogs = false
                }
            } catch {
                await MainActor.run {
                    self.logs = ["Error loading logs: \(error.localizedDescription)"]
                    self.isLoadingLogs = false
                }
            }
        }
    }
}

struct InfoCard: View {
    let title: String
    let value: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.body)
                .fontWeight(.semibold)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
}

enum ServiceSortOrder {
    case name
    case status
    case lastUpdated
}

#Preview {
    ServicesView()
        .environmentObject(AppState())
        .frame(width: 800, height: 600)
}