import Foundation

// MARK: - Service Models
struct ServiceInfo: Codable, Identifiable {
    let id: String
    let name: String
    let status: ServiceStatus
    let port: Int?
    let url: String?
    let description: String?
    let lastUpdated: Date
    let healthCheck: HealthCheck?
    let metrics: ServiceMetrics?
}

enum ServiceStatus: String, Codable, CaseIterable {
    case running = "running"
    case stopped = "stopped"
    case error = "error"
    case starting = "starting"
    case stopping = "stopping"
    
    var displayName: String {
        switch self {
        case .running: return "Running"
        case .stopped: return "Stopped"
        case .error: return "Error"
        case .starting: return "Starting"
        case .stopping: return "Stopping"
        }
    }
    
    var color: String {
        switch self {
        case .running: return "green"
        case .stopped: return "gray"
        case .error: return "red"
        case .starting, .stopping: return "orange"
        }
    }
}

struct HealthCheck: Codable {
    let endpoint: String
    let interval: Int
    let timeout: Int
    let retries: Int
    let lastCheck: Date?
    let status: HealthStatus
}

enum HealthStatus: String, Codable {
    case healthy = "healthy"
    case unhealthy = "unhealthy"
    case unknown = "unknown"
}

struct ServiceMetrics: Codable {
    let cpuUsage: Double
    let memoryUsage: Double
    let requestCount: Int
    let errorRate: Double
    let responseTime: Double
}

// MARK: - Docker Models
struct DockerContainer: Codable, Identifiable {
    let id: String
    let name: String
    let image: String
    let status: ContainerStatus
    let state: String
    let ports: [PortMapping]
    let created: Date
    let started: Date?
    let networks: [String]
    let volumes: [VolumeMount]
    let environment: [String: String]
    let labels: [String: String]
    let stats: ContainerStats?
}

enum ContainerStatus: String, Codable {
    case running = "running"
    case exited = "exited"
    case created = "created"
    case restarting = "restarting"
    case removing = "removing"
    case paused = "paused"
    case dead = "dead"
    
    var color: String {
        switch self {
        case .running: return "green"
        case .exited, .dead: return "red"
        case .created, .paused: return "orange"
        case .restarting, .removing: return "blue"
        }
    }
}

struct PortMapping: Codable {
    let privatePort: Int
    let publicPort: Int?
    let type: String
    let ip: String?
}

struct VolumeMount: Codable {
    let source: String
    let destination: String
    let mode: String
    let rw: Bool
}

struct ContainerStats: Codable {
    let cpuPercent: Double
    let memoryUsage: Int64
    let memoryLimit: Int64
    let networkRx: Int64
    let networkTx: Int64
    let blockRead: Int64
    let blockWrite: Int64
    let pids: Int
}

// MARK: - System Models
struct SystemMetrics: Codable {
    let cpu: CPUMetrics
    let memory: MemoryMetrics
    let disk: DiskMetrics
    let network: NetworkMetrics
    let uptime: TimeInterval
    let loadAverage: [Double]
    let timestamp: Date
}

struct CPUMetrics: Codable {
    let usage: Double
    let cores: Int
    let frequency: Double?
    let temperature: Double?
}

struct MemoryMetrics: Codable {
    let total: Int64
    let used: Int64
    let free: Int64
    let available: Int64
    let cached: Int64
    let buffers: Int64
    let swapTotal: Int64
    let swapUsed: Int64
}

struct DiskMetrics: Codable {
    let total: Int64
    let used: Int64
    let free: Int64
    let readBytes: Int64
    let writeBytes: Int64
    let readOps: Int64
    let writeOps: Int64
}

struct NetworkMetrics: Codable {
    let bytesReceived: Int64
    let bytesSent: Int64
    let packetsReceived: Int64
    let packetsSent: Int64
    let errorsReceived: Int64
    let errorsSent: Int64
}

// MARK: - Notification Models
struct NotificationItem: Identifiable {
    let id: UUID
    let message: String
    let type: NotificationType
    let timestamp: Date
}

enum NotificationType {
    case info
    case success
    case warning
    case error
    
    var color: String {
        switch self {
        case .info: return "blue"
        case .success: return "green"
        case .warning: return "orange"
        case .error: return "red"
        }
    }
    
    var icon: String {
        switch self {
        case .info: return "info.circle"
        case .success: return "checkmark.circle"
        case .warning: return "exclamationmark.triangle"
        case .error: return "xmark.circle"
        }
    }
}

// MARK: - API Response Models
struct APIResponse<T: Codable>: Codable {
    let success: Bool
    let data: T?
    let error: String?
    let timestamp: Date
}

struct PaginatedResponse<T: Codable>: Codable {
    let items: [T]
    let total: Int
    let page: Int
    let pageSize: Int
    let hasNext: Bool
    let hasPrevious: Bool
}

// MARK: - Settings Models
struct AppSettings: Codable {
    var serverURL: String = "http://localhost:3000"
    var refreshInterval: TimeInterval = 30
    var enableNotifications: Bool = true
    var enableMenuBar: Bool = true
    var autoConnect: Bool = true
    var theme: AppTheme = .system
    var logLevel: LogLevel = .info
}

enum AppTheme: String, Codable, CaseIterable {
    case light = "light"
    case dark = "dark"
    case system = "system"
    
    var displayName: String {
        switch self {
        case .light: return "Light"
        case .dark: return "Dark"
        case .system: return "System"
        }
    }
}

enum LogLevel: String, Codable, CaseIterable {
    case trace = "trace"
    case debug = "debug"
    case info = "info"
    case notice = "notice"
    case warning = "warning"
    case error = "error"
    case critical = "critical"
    
    var displayName: String {
        rawValue.capitalized
    }
}