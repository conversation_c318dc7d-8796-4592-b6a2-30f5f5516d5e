import SwiftUI

struct LogsView: View {
    @EnvironmentObject var appState: AppState
    @State private var selectedLogType: LogType = .all
    @State private var selectedLogLevel: LogLevel = .all
    @State private var searchText = ""
    @State private var autoScroll = true
    @State private var showTimestamps = true
    @State private var maxLines = 1000
    @State private var refreshTimer: Timer?
    
    var filteredLogs: [LogEntry] {
        var logs = appState.logs
        
        // Filter by type
        if selectedLogType != .all {
            logs = logs.filter { $0.type == selectedLogType }
        }
        
        // Filter by level
        if selectedLogLevel != .all {
            logs = logs.filter { $0.level == selectedLogLevel }
        }
        
        // Filter by search text
        if !searchText.isEmpty {
            logs = logs.filter { log in
                log.message.localizedCaseInsensitiveContains(searchText) ||
                log.source.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // Limit number of logs
        return Array(logs.suffix(maxLines))
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with controls
            HeaderView()
            
            // Logs content
            LogsContentView()
        }
        .onAppear {
            startAutoRefresh()
        }
        .onDisappear {
            stopAutoRefresh()
        }
    }
    
    @ViewBuilder
    private func HeaderView() -> some View {
        VStack(spacing: 12) {
            // First row: Type and Level filters
            HStack {
                // Log type filter
                Picker("Log Type", selection: $selectedLogType) {
                    ForEach(LogType.allCases, id: \.self) { type in
                        Text(type.displayName).tag(type)
                    }
                }
                .pickerStyle(.segmented)
                .frame(width: 300)
                
                Spacer()
                
                // Log level filter
                Picker("Log Level", selection: $selectedLogLevel) {
                    ForEach(LogLevel.allCases, id: \.self) { level in
                        Text(level.displayName).tag(level)
                    }
                }
                .pickerStyle(.menu)
                .frame(width: 120)
            }
            
            // Second row: Search and controls
            HStack {
                // Search field
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                    
                    TextField("Search logs...", text: $searchText)
                        .textFieldStyle(.plain)
                    
                    if !searchText.isEmpty {
                        Button(action: { searchText = "" }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.secondary)
                        }
                        .buttonStyle(.plain)
                    }
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color(NSColor.textBackgroundColor))
                .clipShape(RoundedRectangle(cornerRadius: 6))
                .frame(maxWidth: 300)
                
                Spacer()
                
                // Controls
                HStack(spacing: 12) {
                    // Max lines stepper
                    HStack {
                        Text("Max:")
                            .font(.caption)
                        
                        Stepper("\(maxLines)", value: $maxLines, in: 100...10000, step: 100)
                            .frame(width: 80)
                    }
                    
                    // Show timestamps toggle
                    Toggle("Timestamps", isOn: $showTimestamps)
                    
                    // Auto scroll toggle
                    Toggle("Auto Scroll", isOn: $autoScroll)
                    
                    // Clear logs button
                    Button("Clear") {
                        appState.clearLogs()
                    }
                    .buttonStyle(.bordered)
                    
                    // Export logs button
                    Button("Export") {
                        exportLogs()
                    }
                    .buttonStyle(.bordered)
                    
                    // Refresh button
                    Button(action: { appState.refreshLogs() }) {
                        Image(systemName: "arrow.clockwise")
                    }
                    .buttonStyle(.borderless)
                }
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    @ViewBuilder
    private func LogsContentView() -> some View {
        if filteredLogs.isEmpty {
            EmptyStateView()
        } else {
            LogsListView()
        }
    }
    
    @ViewBuilder
    private func EmptyStateView() -> some View {
        VStack(spacing: 16) {
            Image(systemName: "doc.text")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("No Logs Available")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text(searchText.isEmpty ? "No logs to display" : "No logs match your search criteria")
                .font(.body)
                .foregroundColor(.secondary)
            
            if !searchText.isEmpty {
                Button("Clear Search") {
                    searchText = ""
                }
                .buttonStyle(.borderedProminent)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    @ViewBuilder
    private func LogsListView() -> some View {
        ScrollViewReader { proxy in
            List {
                ForEach(filteredLogs) { log in
                    LogRowView(log: log, showTimestamp: showTimestamps)
                        .id(log.id)
                }
            }
            .listStyle(.plain)
            .onChange(of: filteredLogs.count) { _, _ in
                if autoScroll && !filteredLogs.isEmpty {
                    withAnimation(.easeOut(duration: 0.3)) {
                        proxy.scrollTo(filteredLogs.last?.id, anchor: .bottom)
                    }
                }
            }
        }
    }
    
    private func startAutoRefresh() {
        refreshTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { _ in
            appState.refreshLogs()
        }
    }
    
    private func stopAutoRefresh() {
        refreshTimer?.invalidate()
        refreshTimer = nil
    }
    
    private func exportLogs() {
        let panel = NSSavePanel()
        panel.allowedContentTypes = [.plainText]
        panel.nameFieldStringValue = "logs-\(Date().formatted(date: .numeric, time: .omitted)).txt"
        
        panel.begin { response in
            if response == .OK, let url = panel.url {
                let logText = filteredLogs.map { log in
                    let timestamp = showTimestamps ? "[\(log.timestamp.formatted())] " : ""
                    let level = "[\(log.level.displayName.uppercased())] "
                    let source = "[\(log.source)] "
                    return "\(timestamp)\(level)\(source)\(log.message)"
                }.joined(separator: "\n")
                
                do {
                    try logText.write(to: url, atomically: true, encoding: .utf8)
                } catch {
                    print("Failed to export logs: \(error)")
                }
            }
        }
    }
}

struct LogRowView: View {
    let log: LogEntry
    let showTimestamp: Bool
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack(alignment: .top, spacing: 8) {
                // Log level indicator
                Circle()
                    .fill(log.level.color)
                    .frame(width: 8, height: 8)
                    .padding(.top, 4)
                
                VStack(alignment: .leading, spacing: 2) {
                    // Header with timestamp, level, and source
                    HStack {
                        if showTimestamp {
                            Text(log.timestamp.formatted(date: .omitted, time: .standard))
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .monospacedDigit()
                        }
                        
                        Text(log.level.displayName.uppercased())
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(log.level.color)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(log.level.color.opacity(0.1))
                            .clipShape(RoundedRectangle(cornerRadius: 4))
                        
                        Text(log.source)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.secondary.opacity(0.1))
                            .clipShape(RoundedRectangle(cornerRadius: 4))
                        
                        Spacer()
                        
                        if log.message.count > 100 {
                            Button(isExpanded ? "Less" : "More") {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    isExpanded.toggle()
                                }
                            }
                            .font(.caption)
                            .buttonStyle(.plain)
                            .foregroundColor(.accentColor)
                        }
                    }
                    
                    // Log message
                    Text(isExpanded ? log.message : String(log.message.prefix(100)) + (log.message.count > 100 ? "..." : ""))
                        .font(.system(.body, design: .monospaced))
                        .textSelection(.enabled)
                        .lineLimit(isExpanded ? nil : 3)
                        .animation(.easeInOut(duration: 0.2), value: isExpanded)
                }
            }
        }
        .padding(.vertical, 4)
        .contextMenu {
            Button("Copy Message") {
                NSPasteboard.general.clearContents()
                NSPasteboard.general.setString(log.message, forType: .string)
            }
            
            Button("Copy Full Entry") {
                let fullEntry = "[\(log.timestamp.formatted())] [\(log.level.displayName.uppercased())] [\(log.source)] \(log.message)"
                NSPasteboard.general.clearContents()
                NSPasteboard.general.setString(fullEntry, forType: .string)
            }
        }
    }
}

// MARK: - Supporting Types

struct LogEntry: Identifiable, Codable {
    let id = UUID()
    let timestamp: Date
    let level: LogLevel
    let source: String
    let message: String
    let type: LogType
    
    init(timestamp: Date = Date(), level: LogLevel, source: String, message: String, type: LogType = .application) {
        self.timestamp = timestamp
        self.level = level
        self.source = source
        self.message = message
        self.type = type
    }
}

enum LogType: String, CaseIterable, Codable {
    case all = "all"
    case application = "application"
    case system = "system"
    case docker = "docker"
    case network = "network"
    case database = "database"
    case security = "security"
    
    var displayName: String {
        switch self {
        case .all: return "All"
        case .application: return "App"
        case .system: return "System"
        case .docker: return "Docker"
        case .network: return "Network"
        case .database: return "Database"
        case .security: return "Security"
        }
    }
}

enum LogLevel: String, CaseIterable, Codable {
    case all = "all"
    case debug = "debug"
    case info = "info"
    case warning = "warning"
    case error = "error"
    case critical = "critical"
    
    var displayName: String {
        switch self {
        case .all: return "All"
        case .debug: return "Debug"
        case .info: return "Info"
        case .warning: return "Warning"
        case .error: return "Error"
        case .critical: return "Critical"
        }
    }
    
    var color: Color {
        switch self {
        case .all: return .primary
        case .debug: return .secondary
        case .info: return .blue
        case .warning: return .orange
        case .error: return .red
        case .critical: return .purple
        }
    }
    
    var priority: Int {
        switch self {
        case .all: return 0
        case .debug: return 1
        case .info: return 2
        case .warning: return 3
        case .error: return 4
        case .critical: return 5
        }
    }
}

// MARK: - AppState Extension

extension AppState {
    func addLog(_ log: LogEntry) {
        DispatchQueue.main.async {
            self.logs.append(log)
            
            // Keep only the last 10,000 logs to prevent memory issues
            if self.logs.count > 10000 {
                self.logs.removeFirst(self.logs.count - 10000)
            }
        }
    }
    
    func clearLogs() {
        DispatchQueue.main.async {
            self.logs.removeAll()
        }
    }
    
    func refreshLogs() {
        // This would typically fetch logs from the backend
        // For now, we'll just trigger a refresh of existing logs
        Task {
            await fetchLogs()
        }
    }
    
    private func fetchLogs() async {
        // Implementation would fetch logs from backend API
        // This is a placeholder for the actual implementation
    }
}

#Preview {
    LogsView()
        .environmentObject({
            let appState = AppState()
            
            // Add sample logs for preview
            appState.logs = [
                LogEntry(level: .info, source: "API", message: "Server started successfully on port 3000", type: .application),
                LogEntry(level: .warning, source: "Docker", message: "Container memory usage is high (85%)", type: .docker),
                LogEntry(level: .error, source: "Database", message: "Connection timeout after 30 seconds", type: .database),
                LogEntry(level: .debug, source: "Network", message: "HTTP request to /api/services completed in 45ms", type: .network),
                LogEntry(level: .critical, source: "Security", message: "Multiple failed login attempts detected", type: .security)
            ]
            
            return appState
        }())
        .frame(width: 1000, height: 700)
}