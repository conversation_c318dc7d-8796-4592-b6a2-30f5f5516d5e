import SwiftUI

struct DockerView: View {
    @EnvironmentObject var appState: AppState
    @State private var searchText = ""
    @State private var selectedContainer: DockerContainer?
    @State private var showingContainerDetails = false
    @State private var sortOrder: ContainerSortOrder = .name
    @State private var filterStatus: DockerContainerStatus? = nil
    @State private var showingImageManagement = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with controls
            HeaderView()
            
            // Containers list
            if filteredContainers.isEmpty {
                EmptyStateView()
            } else {
                ContainersListView()
            }
        }
        .sheet(isPresented: $showingContainerDetails) {
            if let container = selectedContainer {
                ContainerDetailView(container: container)
            }
        }
        .sheet(isPresented: $showingImageManagement) {
            ImageManagementView()
        }
    }
    
    private var filteredContainers: [DockerContainer] {
        var containers = appState.dockerContainers
        
        // Apply search filter
        if !searchText.isEmpty {
            containers = containers.filter { container in
                container.name.localizedCaseInsensitiveContains(searchText) ||
                container.image.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // Apply status filter
        if let filterStatus = filterStatus {
            containers = containers.filter { $0.status == filterStatus }
        }
        
        // Apply sorting
        switch sortOrder {
        case .name:
            containers.sort { $0.name < $1.name }
        case .status:
            containers.sort { $0.status.rawValue < $1.status.rawValue }
        case .created:
            containers.sort { $0.created > $1.created }
        case .image:
            containers.sort { $0.image < $1.image }
        }
        
        return containers
    }
    
    @ViewBuilder
    private func HeaderView() -> some View {
        VStack(spacing: 12) {
            HStack {
                // Search bar
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                    
                    TextField("Search containers...", text: $searchText)
                        .textFieldStyle(.plain)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 6)
                .background(Color(NSColor.controlBackgroundColor))
                .clipShape(RoundedRectangle(cornerRadius: 6))
                
                Spacer()
                
                // Action buttons
                Button("Images") {
                    showingImageManagement = true
                }
                .buttonStyle(.bordered)
                
                // Filter and sort controls
                Menu {
                    Button("All Containers") { filterStatus = nil }
                    Button("Running") { filterStatus = .running }
                    Button("Exited") { filterStatus = .exited }
                    Button("Paused") { filterStatus = .paused }
                    Button("Restarting") { filterStatus = .restarting }
                } label: {
                    Label("Filter", systemImage: "line.3.horizontal.decrease.circle")
                }
                .menuStyle(.borderlessButton)
                
                Menu {
                    Button("Name") { sortOrder = .name }
                    Button("Status") { sortOrder = .status }
                    Button("Created") { sortOrder = .created }
                    Button("Image") { sortOrder = .image }
                } label: {
                    Label("Sort", systemImage: "arrow.up.arrow.down")
                }
                .menuStyle(.borderlessButton)
                
                Button(action: { appState.refreshDockerContainers() }) {
                    Image(systemName: "arrow.clockwise")
                }
                .buttonStyle(.borderless)
            }
            
            // Status summary
            HStack {
                ContainerStatusSummaryItem(
                    title: "Total",
                    count: appState.dockerContainers.count,
                    color: .primary
                )
                
                ContainerStatusSummaryItem(
                    title: "Running",
                    count: appState.dockerContainers.filter { $0.status == .running }.count,
                    color: .green
                )
                
                ContainerStatusSummaryItem(
                    title: "Exited",
                    count: appState.dockerContainers.filter { $0.status == .exited }.count,
                    color: .red
                )
                
                ContainerStatusSummaryItem(
                    title: "Paused",
                    count: appState.dockerContainers.filter { $0.status == .paused }.count,
                    color: .orange
                )
                
                Spacer()
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    @ViewBuilder
    private func EmptyStateView() -> some View {
        VStack(spacing: 16) {
            Image(systemName: "shippingbox")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("No Containers Found")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text(searchText.isEmpty ? "No Docker containers are running" : "No containers match your search")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            if !searchText.isEmpty {
                Button("Clear Search") {
                    searchText = ""
                }
                .buttonStyle(.borderedProminent)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    @ViewBuilder
    private func ContainersListView() -> some View {
        List(filteredContainers) { container in
            ContainerRowView(container: container) {
                selectedContainer = container
                showingContainerDetails = true
            }
            .listRowSeparator(.hidden)
            .listRowBackground(Color.clear)
        }
        .listStyle(.plain)
    }
}

struct ContainerRowView: View {
    let container: DockerContainer
    let onTap: () -> Void
    @EnvironmentObject var appState: AppState
    @State private var isPerformingAction = false
    
    var body: some View {
        HStack(spacing: 16) {
            // Status indicator
            VStack {
                Circle()
                    .fill(container.status.color)
                    .frame(width: 12, height: 12)
                
                if container.status == .running {
                    Circle()
                        .fill(container.status.color)
                        .frame(width: 8, height: 8)
                        .scaleEffect(isPerformingAction ? 1.2 : 0.8)
                        .opacity(isPerformingAction ? 0.5 : 1.0)
                        .animation(.easeInOut(duration: 0.6).repeatForever(), value: isPerformingAction)
                }
            }
            .frame(width: 20)
            
            // Container info
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(container.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Spacer()
                    
                    Text(container.status.displayName)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(container.status.color.opacity(0.2))
                        .foregroundColor(container.status.color)
                        .clipShape(Capsule())
                }
                
                Text(container.image)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
                
                HStack {
                    if !container.ports.isEmpty {
                        Label(container.ports.joined(separator: ", "), systemImage: "network")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                    
                    Spacer()
                    
                    Text("Created \(container.created, style: .relative)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Action buttons
            HStack(spacing: 8) {
                if container.status == .running {
                    Button(action: { stopContainer() }) {
                        Image(systemName: "stop.fill")
                    }
                    .buttonStyle(.borderless)
                    .foregroundColor(.red)
                    .disabled(isPerformingAction)
                    
                    Button(action: { restartContainer() }) {
                        Image(systemName: "arrow.clockwise")
                    }
                    .buttonStyle(.borderless)
                    .foregroundColor(.orange)
                    .disabled(isPerformingAction)
                    
                    Button(action: { pauseContainer() }) {
                        Image(systemName: "pause.fill")
                    }
                    .buttonStyle(.borderless)
                    .foregroundColor(.yellow)
                    .disabled(isPerformingAction)
                } else if container.status == .paused {
                    Button(action: { unpauseContainer() }) {
                        Image(systemName: "play.fill")
                    }
                    .buttonStyle(.borderless)
                    .foregroundColor(.green)
                    .disabled(isPerformingAction)
                } else {
                    Button(action: { startContainer() }) {
                        Image(systemName: "play.fill")
                    }
                    .buttonStyle(.borderless)
                    .foregroundColor(.green)
                    .disabled(isPerformingAction)
                    
                    Button(action: { removeContainer() }) {
                        Image(systemName: "trash")
                    }
                    .buttonStyle(.borderless)
                    .foregroundColor(.red)
                    .disabled(isPerformingAction)
                }
                
                Button(action: onTap) {
                    Image(systemName: "info.circle")
                }
                .buttonStyle(.borderless)
                .foregroundColor(.blue)
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 8))
        .shadow(color: .black.opacity(0.1), radius: 1, x: 0, y: 1)
        .onAppear {
            if container.status == .running {
                isPerformingAction = true
            }
        }
    }
    
    private func startContainer() {
        performAction {
            try await appState.startDockerContainer(container.id)
        }
    }
    
    private func stopContainer() {
        performAction {
            try await appState.stopDockerContainer(container.id)
        }
    }
    
    private func restartContainer() {
        performAction {
            try await appState.restartDockerContainer(container.id)
        }
    }
    
    private func pauseContainer() {
        performAction {
            try await appState.pauseDockerContainer(container.id)
        }
    }
    
    private func unpauseContainer() {
        performAction {
            try await appState.unpauseDockerContainer(container.id)
        }
    }
    
    private func removeContainer() {
        performAction {
            try await appState.removeDockerContainer(container.id)
        }
    }
    
    private func performAction(_ action: @escaping () async throws -> Void) {
        isPerformingAction = true
        Task {
            try? await action()
            await MainActor.run {
                isPerformingAction = false
            }
        }
    }
}

struct ContainerStatusSummaryItem: View {
    let title: String
    let count: Int
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 2) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text("\(count)")
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
    }
}

struct ContainerDetailView: View {
    let container: DockerContainer
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var appState: AppState
    @State private var logs: [String] = []
    @State private var isLoadingLogs = false
    @State private var stats: ContainerStats?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Container header
                ContainerHeaderView()
                
                // Tabs
                TabView {
                    // Overview tab
                    ContainerOverviewTab()
                        .tabItem {
                            Label("Overview", systemImage: "info.circle")
                        }
                    
                    // Stats tab
                    ContainerStatsTab()
                        .tabItem {
                            Label("Stats", systemImage: "chart.bar")
                        }
                    
                    // Logs tab
                    ContainerLogsTab()
                        .tabItem {
                            Label("Logs", systemImage: "doc.text")
                        }
                    
                    // Environment tab
                    ContainerEnvironmentTab()
                        .tabItem {
                            Label("Environment", systemImage: "gear")
                        }
                }
            }
            .navigationTitle(container.name)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Close") {
                        dismiss()
                    }
                }
            }
        }
        .frame(width: 700, height: 600)
    }
    
    @ViewBuilder
    private func ContainerHeaderView() -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Circle()
                        .fill(container.status.color)
                        .frame(width: 16, height: 16)
                    
                    Text(container.status.displayName)
                        .font(.headline)
                        .foregroundColor(container.status.color)
                }
                
                Text(container.image)
                    .font(.body)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Quick actions
            HStack(spacing: 8) {
                if container.status == .running {
                    Button("Stop") {
                        Task {
                            try? await appState.stopDockerContainer(container.id)
                        }
                    }
                    .buttonStyle(.bordered)
                    
                    Button("Restart") {
                        Task {
                            try? await appState.restartDockerContainer(container.id)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                } else {
                    Button("Start") {
                        Task {
                            try? await appState.startDockerContainer(container.id)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                }
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    @ViewBuilder
    private func ContainerOverviewTab() -> some View {
        ScrollView {
            LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 16) {
                InfoCard(title: "Container ID", value: String(container.id.prefix(12)))
                InfoCard(title: "Image", value: container.image)
                InfoCard(title: "Status", value: container.status.displayName)
                InfoCard(title: "Created", value: container.created.formatted())
                InfoCard(title: "Ports", value: container.ports.isEmpty ? "None" : container.ports.joined(separator: ", "))
                InfoCard(title: "Command", value: container.command ?? "N/A")
            }
            .padding()
        }
    }
    
    @ViewBuilder
    private func ContainerStatsTab() -> some View {
        VStack {
            HStack {
                Button("Refresh Stats") {
                    loadStats()
                }
                .buttonStyle(.bordered)
                
                Spacer()
            }
            .padding()
            
            if let stats = stats {
                ScrollView {
                    LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 16) {
                        StatsCard(title: "CPU Usage", value: "\(String(format: "%.1f", stats.cpuPercent))%", color: .blue)
                        StatsCard(title: "Memory Usage", value: formatBytes(stats.memoryUsage), color: .green)
                        StatsCard(title: "Memory Limit", value: formatBytes(stats.memoryLimit), color: .green)
                        StatsCard(title: "Network I/O", value: "↓\(formatBytes(stats.networkRx)) ↑\(formatBytes(stats.networkTx))", color: .orange)
                        StatsCard(title: "Block I/O", value: "↓\(formatBytes(stats.blockRead)) ↑\(formatBytes(stats.blockWrite))", color: .purple)
                        StatsCard(title: "PIDs", value: "\(stats.pids)", color: .red)
                    }
                    .padding()
                }
            } else {
                VStack {
                    Text("No stats available")
                        .foregroundColor(.secondary)
                    
                    if container.status == .running {
                        Button("Load Stats") {
                            loadStats()
                        }
                        .buttonStyle(.borderedProminent)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .onAppear {
            if container.status == .running {
                loadStats()
            }
        }
    }
    
    @ViewBuilder
    private func ContainerLogsTab() -> some View {
        VStack {
            HStack {
                Button("Refresh Logs") {
                    loadLogs()
                }
                .buttonStyle(.bordered)
                
                Spacer()
                
                if isLoadingLogs {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            .padding()
            
            ScrollView {
                LazyVStack(alignment: .leading, spacing: 4) {
                    ForEach(logs.indices, id: \.self) { index in
                        Text(logs[index])
                            .font(.system(.caption, design: .monospaced))
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                }
                .padding()
            }
            .background(Color(NSColor.textBackgroundColor))
            .clipShape(RoundedRectangle(cornerRadius: 8))
            .padding(.horizontal)
        }
        .onAppear {
            loadLogs()
        }
    }
    
    @ViewBuilder
    private func ContainerEnvironmentTab() -> some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                Text("Environment Variables")
                    .font(.headline)
                
                if container.environment.isEmpty {
                    Text("No environment variables set")
                        .foregroundColor(.secondary)
                } else {
                    LazyVStack(alignment: .leading, spacing: 8) {
                        ForEach(container.environment.keys.sorted(), id: \.self) { key in
                            HStack {
                                Text(key)
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)
                                
                                Spacer()
                                
                                Text(container.environment[key] ?? "")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .lineLimit(1)
                            }
                            .padding(.vertical, 4)
                            .padding(.horizontal, 8)
                            .background(Color(NSColor.controlBackgroundColor))
                            .clipShape(RoundedRectangle(cornerRadius: 4))
                        }
                    }
                }
            }
            .padding()
        }
    }
    
    private func loadLogs() {
        isLoadingLogs = true
        Task {
            do {
                let containerLogs = try await appState.getDockerContainerLogs(container.id)
                await MainActor.run {
                    self.logs = containerLogs
                    self.isLoadingLogs = false
                }
            } catch {
                await MainActor.run {
                    self.logs = ["Error loading logs: \(error.localizedDescription)"]
                    self.isLoadingLogs = false
                }
            }
        }
    }
    
    private func loadStats() {
        Task {
            do {
                let containerStats = try await appState.getDockerContainerStats(container.id)
                await MainActor.run {
                    self.stats = containerStats
                }
            } catch {
                print("Error loading stats: \(error.localizedDescription)")
            }
        }
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .binary
        return formatter.string(fromByteCount: bytes)
    }
}

struct StatsCard: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.body)
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
}

struct ImageManagementView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var appState: AppState
    @State private var images: [DockerImage] = []
    @State private var isLoading = false
    
    var body: some View {
        NavigationView {
            VStack {
                if isLoading {
                    ProgressView("Loading images...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if images.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "photo")
                            .font(.system(size: 48))
                            .foregroundColor(.secondary)
                        
                        Text("No Docker Images")
                            .font(.title2)
                            .fontWeight(.semibold)
                        
                        Button("Refresh") {
                            loadImages()
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    List(images) { image in
                        ImageRowView(image: image)
                    }
                }
            }
            .navigationTitle("Docker Images")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .primaryAction) {
                    Button("Refresh") {
                        loadImages()
                    }
                }
            }
        }
        .frame(width: 600, height: 500)
        .onAppear {
            loadImages()
        }
    }
    
    private func loadImages() {
        isLoading = true
        Task {
            do {
                let dockerImages = try await appState.getDockerImages()
                await MainActor.run {
                    self.images = dockerImages
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.images = []
                    self.isLoading = false
                }
            }
        }
    }
}

struct ImageRowView: View {
    let image: DockerImage
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(image.repository)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text("Tag: \(image.tag)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text("Size: \(formatBytes(image.size))")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text("Created \(image.created, style: .relative)")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .binary
        return formatter.string(fromByteCount: bytes)
    }
}

enum ContainerSortOrder {
    case name
    case status
    case created
    case image
}

// Additional models for container stats and images
struct ContainerStats: Codable {
    let cpuPercent: Double
    let memoryUsage: Int64
    let memoryLimit: Int64
    let networkRx: Int64
    let networkTx: Int64
    let blockRead: Int64
    let blockWrite: Int64
    let pids: Int
}

struct DockerImage: Codable, Identifiable {
    let id: String
    let repository: String
    let tag: String
    let size: Int64
    let created: Date
}

#Preview {
    DockerView()
        .environmentObject(AppState())
        .frame(width: 800, height: 600)
}