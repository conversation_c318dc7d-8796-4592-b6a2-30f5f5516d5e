import SwiftUI
import Charts

struct SystemMetricsView: View {
    @EnvironmentObject var appState: AppState
    @State private var selectedTimeRange: TimeRange = .hour
    @State private var autoRefresh = true
    @State private var refreshTimer: Timer?
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with controls
            HeaderView()
            
            // Metrics content
            if let metrics = appState.systemMetrics {
                MetricsContentView(metrics: metrics)
            } else {
                EmptyStateView()
            }
        }
        .onAppear {
            startAutoRefresh()
        }
        .onDisappear {
            stopAutoRefresh()
        }
    }
    
    @ViewBuilder
    private func HeaderView() -> some View {
        HStack {
            // Time range selector
            Picker("Time Range", selection: $selectedTimeRange) {
                ForEach(TimeRange.allCases, id: \.self) { range in
                    Text(range.displayName).tag(range)
                }
            }
            .pickerStyle(.segmented)
            .frame(width: 300)
            
            Spacer()
            
            // Auto refresh toggle
            Toggle("Auto Refresh", isOn: $autoRefresh)
                .onChange(of: autoRefresh) { _, newValue in
                    if newValue {
                        startAutoRefresh()
                    } else {
                        stopAutoRefresh()
                    }
                }
            
            // Manual refresh button
            Button(action: { appState.refreshSystemMetrics() }) {
                Image(systemName: "arrow.clockwise")
            }
            .buttonStyle(.borderless)
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    @ViewBuilder
    private func EmptyStateView() -> some View {
        VStack(spacing: 16) {
            Image(systemName: "chart.bar")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("No Metrics Available")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("System metrics are not available")
                .font(.body)
                .foregroundColor(.secondary)
            
            Button("Refresh") {
                appState.refreshSystemMetrics()
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    @ViewBuilder
    private func MetricsContentView(metrics: SystemMetrics) -> some View {
        ScrollView {
            LazyVGrid(columns: gridColumns, spacing: 20) {
                // CPU Metrics
                CPUMetricsCard(metrics: metrics)
                
                // Memory Metrics
                MemoryMetricsCard(metrics: metrics)
                
                // Disk Metrics
                DiskMetricsCard(metrics: metrics)
                
                // Network Metrics
                NetworkMetricsCard(metrics: metrics)
                
                // Load Average
                LoadAverageCard(metrics: metrics)
                
                // Process Information
                ProcessInfoCard(metrics: metrics)
            }
            .padding()
        }
    }
    
    private var gridColumns: [GridItem] {
        [
            GridItem(.flexible(), spacing: 20),
            GridItem(.flexible(), spacing: 20)
        ]
    }
    
    private func startAutoRefresh() {
        guard autoRefresh else { return }
        
        refreshTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { _ in
            appState.refreshSystemMetrics()
        }
    }
    
    private func stopAutoRefresh() {
        refreshTimer?.invalidate()
        refreshTimer = nil
    }
}

struct CPUMetricsCard: View {
    let metrics: SystemMetrics
    
    var body: some View {
        MetricsCard(title: "CPU Usage", icon: "cpu") {
            VStack(spacing: 16) {
                // Overall CPU usage
                HStack {
                    CircularProgressView(
                        progress: metrics.cpu.usage / 100,
                        title: "Overall",
                        value: "\(Int(metrics.cpu.usage))%",
                        color: cpuColor(metrics.cpu.usage)
                    )
                    
                    VStack(alignment: .leading, spacing: 8) {
                        MetricRow(label: "User", value: "\(String(format: "%.1f", metrics.cpu.user))%")
                        MetricRow(label: "System", value: "\(String(format: "%.1f", metrics.cpu.system))%")
                        MetricRow(label: "Idle", value: "\(String(format: "%.1f", metrics.cpu.idle))%")
                        MetricRow(label: "Cores", value: "\(metrics.cpu.cores)")
                    }
                    
                    Spacer()
                }
                
                // CPU cores breakdown if available
                if !metrics.cpu.perCoreUsage.isEmpty {
                    Divider()
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Per Core Usage")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: min(4, metrics.cpu.perCoreUsage.count)), spacing: 8) {
                            ForEach(Array(metrics.cpu.perCoreUsage.enumerated()), id: \.offset) { index, usage in
                                VStack {
                                    Text("\(Int(usage))%")
                                        .font(.caption2)
                                        .fontWeight(.semibold)
                                    
                                    ProgressView(value: usage, total: 100)
                                        .progressViewStyle(LinearProgressViewStyle(tint: cpuColor(usage)))
                                        .frame(height: 4)
                                    
                                    Text("Core \(index)")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    private func cpuColor(_ usage: Double) -> Color {
        switch usage {
        case 0..<50: return .green
        case 50..<80: return .orange
        default: return .red
        }
    }
}

struct MemoryMetricsCard: View {
    let metrics: SystemMetrics
    
    var body: some View {
        MetricsCard(title: "Memory Usage", icon: "memorychip") {
            VStack(spacing: 16) {
                HStack {
                    CircularProgressView(
                        progress: Double(metrics.memory.used) / Double(metrics.memory.total),
                        title: "Used",
                        value: formatBytes(metrics.memory.used),
                        color: memoryColor(Double(metrics.memory.used) / Double(metrics.memory.total))
                    )
                    
                    VStack(alignment: .leading, spacing: 8) {
                        MetricRow(label: "Total", value: formatBytes(metrics.memory.total))
                        MetricRow(label: "Available", value: formatBytes(metrics.memory.available))
                        MetricRow(label: "Free", value: formatBytes(metrics.memory.free))
                        MetricRow(label: "Cached", value: formatBytes(metrics.memory.cached))
                    }
                    
                    Spacer()
                }
                
                // Memory breakdown chart
                Chart {
                    SectorMark(
                        angle: .value("Used", metrics.memory.used),
                        innerRadius: .ratio(0.6),
                        angularInset: 2
                    )
                    .foregroundStyle(.red)
                    .opacity(0.8)
                    
                    SectorMark(
                        angle: .value("Cached", metrics.memory.cached),
                        innerRadius: .ratio(0.6),
                        angularInset: 2
                    )
                    .foregroundStyle(.orange)
                    .opacity(0.8)
                    
                    SectorMark(
                        angle: .value("Free", metrics.memory.free),
                        innerRadius: .ratio(0.6),
                        angularInset: 2
                    )
                    .foregroundStyle(.green)
                    .opacity(0.8)
                }
                .frame(height: 100)
            }
        }
    }
    
    private func memoryColor(_ usage: Double) -> Color {
        switch usage {
        case 0..<0.6: return .green
        case 0.6..<0.8: return .orange
        default: return .red
        }
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .binary
        return formatter.string(fromByteCount: bytes)
    }
}

struct DiskMetricsCard: View {
    let metrics: SystemMetrics
    
    var body: some View {
        MetricsCard(title: "Disk Usage", icon: "internaldrive") {
            VStack(spacing: 16) {
                HStack {
                    CircularProgressView(
                        progress: Double(metrics.disk.used) / Double(metrics.disk.total),
                        title: "Used",
                        value: formatBytes(metrics.disk.used),
                        color: diskColor(Double(metrics.disk.used) / Double(metrics.disk.total))
                    )
                    
                    VStack(alignment: .leading, spacing: 8) {
                        MetricRow(label: "Total", value: formatBytes(metrics.disk.total))
                        MetricRow(label: "Available", value: formatBytes(metrics.disk.available))
                        MetricRow(label: "Read/s", value: formatBytes(metrics.disk.readBytesPerSec))
                        MetricRow(label: "Write/s", value: formatBytes(metrics.disk.writeBytesPerSec))
                    }
                    
                    Spacer()
                }
                
                // Disk I/O chart
                if metrics.disk.readBytesPerSec > 0 || metrics.disk.writeBytesPerSec > 0 {
                    Divider()
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Disk I/O")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        HStack {
                            VStack {
                                Text("Read")
                                    .font(.caption2)
                                    .foregroundColor(.blue)
                                
                                Rectangle()
                                    .fill(.blue)
                                    .frame(width: max(4, CGFloat(metrics.disk.readBytesPerSec) / 1024 / 1024), height: 20)
                                    .animation(.easeInOut, value: metrics.disk.readBytesPerSec)
                            }
                            
                            VStack {
                                Text("Write")
                                    .font(.caption2)
                                    .foregroundColor(.orange)
                                
                                Rectangle()
                                    .fill(.orange)
                                    .frame(width: max(4, CGFloat(metrics.disk.writeBytesPerSec) / 1024 / 1024), height: 20)
                                    .animation(.easeInOut, value: metrics.disk.writeBytesPerSec)
                            }
                        }
                    }
                }
            }
        }
    }
    
    private func diskColor(_ usage: Double) -> Color {
        switch usage {
        case 0..<0.7: return .green
        case 0.7..<0.9: return .orange
        default: return .red
        }
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .binary
        return formatter.string(fromByteCount: bytes)
    }
}

struct NetworkMetricsCard: View {
    let metrics: SystemMetrics
    
    var body: some View {
        MetricsCard(title: "Network Activity", icon: "network") {
            VStack(spacing: 16) {
                HStack {
                    VStack(alignment: .leading, spacing: 8) {
                        MetricRow(label: "Received", value: formatBytes(metrics.network.bytesReceived), color: .green)
                        MetricRow(label: "Sent", value: formatBytes(metrics.network.bytesSent), color: .blue)
                        MetricRow(label: "Packets In", value: "\(metrics.network.packetsReceived)")
                        MetricRow(label: "Packets Out", value: "\(metrics.network.packetsSent)")
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 8) {
                        MetricRow(label: "Download/s", value: formatBytes(metrics.network.downloadBytesPerSec), color: .green)
                        MetricRow(label: "Upload/s", value: formatBytes(metrics.network.uploadBytesPerSec), color: .blue)
                        MetricRow(label: "Errors", value: "\(metrics.network.errors)")
                        MetricRow(label: "Drops", value: "\(metrics.network.drops)")
                    }
                }
                
                // Network activity visualization
                if metrics.network.downloadBytesPerSec > 0 || metrics.network.uploadBytesPerSec > 0 {
                    Divider()
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Real-time Activity")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        HStack {
                            VStack {
                                HStack {
                                    Image(systemName: "arrow.down")
                                        .foregroundColor(.green)
                                        .font(.caption)
                                    Text("Download")
                                        .font(.caption2)
                                }
                                
                                ProgressView(value: Double(metrics.network.downloadBytesPerSec), total: Double(max(metrics.network.downloadBytesPerSec, metrics.network.uploadBytesPerSec, 1)))
                                    .progressViewStyle(LinearProgressViewStyle(tint: .green))
                            }
                            
                            VStack {
                                HStack {
                                    Image(systemName: "arrow.up")
                                        .foregroundColor(.blue)
                                        .font(.caption)
                                    Text("Upload")
                                        .font(.caption2)
                                }
                                
                                ProgressView(value: Double(metrics.network.uploadBytesPerSec), total: Double(max(metrics.network.downloadBytesPerSec, metrics.network.uploadBytesPerSec, 1)))
                                    .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                            }
                        }
                    }
                }
            }
        }
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .binary
        return formatter.string(fromByteCount: bytes)
    }
}

struct LoadAverageCard: View {
    let metrics: SystemMetrics
    
    var body: some View {
        MetricsCard(title: "Load Average", icon: "speedometer") {
            VStack(spacing: 16) {
                HStack(spacing: 20) {
                    LoadIndicator(title: "1 min", value: metrics.loadAverage[0], cores: metrics.cpu.cores)
                    LoadIndicator(title: "5 min", value: metrics.loadAverage[1], cores: metrics.cpu.cores)
                    LoadIndicator(title: "15 min", value: metrics.loadAverage[2], cores: metrics.cpu.cores)
                }
                
                Divider()
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("System Information")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    MetricRow(label: "Uptime", value: formatUptime(metrics.uptime))
                    MetricRow(label: "Boot Time", value: metrics.bootTime.formatted())
                    MetricRow(label: "Processes", value: "\(metrics.processes)")
                }
            }
        }
    }
    
    private func formatUptime(_ uptime: TimeInterval) -> String {
        let days = Int(uptime) / 86400
        let hours = (Int(uptime) % 86400) / 3600
        let minutes = (Int(uptime) % 3600) / 60
        
        if days > 0 {
            return "\(days)d \(hours)h \(minutes)m"
        } else if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
}

struct ProcessInfoCard: View {
    let metrics: SystemMetrics
    
    var body: some View {
        MetricsCard(title: "Process Information", icon: "list.bullet") {
            VStack(spacing: 16) {
                HStack {
                    VStack(alignment: .leading, spacing: 8) {
                        MetricRow(label: "Total Processes", value: "\(metrics.processes)")
                        MetricRow(label: "Running", value: "\(metrics.runningProcesses)", color: .green)
                        MetricRow(label: "Sleeping", value: "\(metrics.sleepingProcesses)", color: .blue)
                        MetricRow(label: "Zombie", value: "\(metrics.zombieProcesses)", color: .red)
                    }
                    
                    Spacer()
                    
                    // Process distribution chart
                    Chart {
                        SectorMark(
                            angle: .value("Running", metrics.runningProcesses),
                            innerRadius: .ratio(0.6)
                        )
                        .foregroundStyle(.green)
                        
                        SectorMark(
                            angle: .value("Sleeping", metrics.sleepingProcesses),
                            innerRadius: .ratio(0.6)
                        )
                        .foregroundStyle(.blue)
                        
                        if metrics.zombieProcesses > 0 {
                            SectorMark(
                                angle: .value("Zombie", metrics.zombieProcesses),
                                innerRadius: .ratio(0.6)
                            )
                            .foregroundStyle(.red)
                        }
                    }
                    .frame(width: 80, height: 80)
                }
            }
        }
    }
}

// MARK: - Supporting Views

struct MetricsCard<Content: View>: View {
    let title: String
    let icon: String
    let content: Content
    
    init(title: String, icon: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.icon = icon
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.accentColor)
                
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            content
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct MetricRow: View {
    let label: String
    let value: String
    var color: Color = .primary
    
    var body: some View {
        HStack {
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
    }
}

struct LoadIndicator: View {
    let title: String
    let value: Double
    let cores: Int
    
    var body: some View {
        VStack {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(String(format: "%.2f", value))
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(loadColor)
            
            ProgressView(value: min(value / Double(cores), 1.0))
                .progressViewStyle(LinearProgressViewStyle(tint: loadColor))
                .frame(width: 60)
        }
    }
    
    private var loadColor: Color {
        let normalizedLoad = value / Double(cores)
        switch normalizedLoad {
        case 0..<0.7: return .green
        case 0.7..<1.0: return .orange
        default: return .red
        }
    }
}

enum TimeRange: CaseIterable {
    case minute
    case hour
    case day
    case week
    
    var displayName: String {
        switch self {
        case .minute: return "1m"
        case .hour: return "1h"
        case .day: return "1d"
        case .week: return "1w"
        }
    }
}

#Preview {
    SystemMetricsView()
        .environmentObject(AppState())
        .frame(width: 1000, height: 700)
}