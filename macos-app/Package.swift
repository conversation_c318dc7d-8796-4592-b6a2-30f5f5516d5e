// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "WOWMacOS",
    platforms: [
        .macOS(.v13)
    ],
    products: [
        .executable(
            name: "WOWMacOS",
            targets: ["WOW<PERSON>ac<PERSON>"]
        ),
    ],
    dependencies: [
        .package(url: "https://github.com/Alamofire/Alamofire.git", from: "5.8.0"),
        .package(url: "https://github.com/socketio/socket.io-client-swift", from: "16.0.0"),
        .package(url: "https://github.com/apple/swift-log.git", from: "1.0.0"),
        .package(url: "https://github.com/SwiftyJSON/SwiftyJSON.git", from: "4.0.0")
    ],
    targets: [
        .executableTarget(
            name: "WOWMacOS",
            dependencies: [
                "Alamofire",
                .product(name: "Socket<PERSON>", package: "socket.io-client-swift"),
                .product(name: "Logging", package: "swift-log"),
                "SwiftyJSON"
            ],
            path: "Sources"
        ),
        .testTarget(
            name: "WOWMacOSTests",
            dependencies: ["WOWMacOS"]
        ),
    ]
)