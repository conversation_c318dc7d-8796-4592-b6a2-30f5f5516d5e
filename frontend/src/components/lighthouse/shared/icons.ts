// Lighthouse-specific icon exports
// This file provides a clean interface for icons used in lighthouse components

// Import from the actual icon files in the icons directory
export { ActivityIcon } from '../../ui/icons/activity';
export { SparklesIcon } from '../../ui/icons/sparkles';
export { ClockIcon } from '../../ui/icons/clock';
export { UserIcon } from '../../ui/icons/user';
export { HomeIcon } from '../../ui/icons/home';
export { BookTextIcon } from '../../ui/icons/book-text';
export { TelescopeIcon } from '../../ui/icons/telescope';
export { CpuIcon } from '../../ui/icons/cpu';
export { MessageCircleIcon } from '../../ui/icons/message-circle';
export { FileTextIcon } from '../../ui/icons/file-text';
export { ChartLineIcon } from '../../ui/icons/chart-line';
export { ArrowRightIcon } from '../../ui/icons/arrow-right';
export { LoaderPinwheelIcon } from '../../ui/icons/loader-pinwheel';
export { HistoryIcon } from '../../ui/icons/history';
export { LinkIcon } from '../../ui/icons/link';
export { SettingsIcon } from '../../ui/icons/settings';

// Export the new icons we created
export { SendIcon } from '../../ui/icons/send';
export { MicIcon } from '../../ui/icons/mic';
export { PaperclipIcon } from '../../ui/icons/paperclip';
export { MoreHorizontalIcon } from '../../ui/icons/more-horizontal';
export { CheckCircleIcon } from '../../ui/icons/check-circle';
export { AlertTriangleIcon } from '../../ui/icons/alert-triangle';

// For any missing icons, we'll import from existing icon files or create simple ones
export { RefreshCWIcon as RefreshCw } from '../../ui/icons/refresh-cw';

// Create simple fallback components for missing icons
import React from 'react';

interface IconProps {
  size?: number;
  className?: string;
}

export const Bug: React.FC<IconProps> = ({ size = 24, className }) => (
  React.createElement('svg', {
    width: size,
    height: size,
    viewBox: '0 0 24 24',
    fill: 'none',
    stroke: 'currentColor',
    strokeWidth: '2',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    className: className
  },
  React.createElement('path', { d: 'm8 2 1.88 1.88' }),
  React.createElement('path', { d: 'M14.12 3.88 16 2' }),
  React.createElement('path', { d: 'M9 7.13v-1a3.003 3.003 0 1 1 6 0v1' }),
  React.createElement('path', { d: 'M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6' }),
  React.createElement('path', { d: 'M12 20v-9' }),
  React.createElement('path', { d: 'M6.53 9C4.6 8.8 3 7.1 3 5' }),
  React.createElement('path', { d: 'M6 13H2' }),
  React.createElement('path', { d: 'M3 21c0-2.1 1.7-3.9 3.8-4' }),
  React.createElement('path', { d: 'M20.97 5c0 2.1-1.6 3.8-3.5 4' }),
  React.createElement('path', { d: 'M22 13h-4' }),
  React.createElement('path', { d: 'M17.2 17c2.1.1 3.8 1.9 3.8 4' })
  )
);

export const XIcon: React.FC<IconProps> = ({ size = 24, className }) => (
  React.createElement('svg', {
    width: size,
    height: size,
    viewBox: '0 0 24 24',
    fill: 'none',
    stroke: 'currentColor',
    strokeWidth: '2',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    className: className
  },
  React.createElement('path', { d: 'M18 6 6 18' }),
  React.createElement('path', { d: 'm6 6 12 12' })
  )
);

export const Lightbulb: React.FC<IconProps> = ({ size = 24, className }) => (
  React.createElement('svg', {
    width: size,
    height: size,
    viewBox: '0 0 24 24',
    fill: 'none',
    stroke: 'currentColor',
    strokeWidth: '2',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    className: className
  },
  React.createElement('path', { d: 'M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5' }),
  React.createElement('path', { d: 'M9 18h6' }),
  React.createElement('path', { d: 'M10 22h4' })
  )
);

export const Link: React.FC<IconProps> = ({ size = 24, className }) => (
  React.createElement('svg', {
    width: size,
    height: size,
    viewBox: '0 0 24 24',
    fill: 'none',
    stroke: 'currentColor',
    strokeWidth: '2',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    className: className
  },
  React.createElement('path', { d: 'M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71' }),
  React.createElement('path', { d: 'M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71' })
  )
);

export const Bot: React.FC<IconProps> = ({ size = 24, className }) => (
  React.createElement('svg', {
    width: size,
    height: size,
    viewBox: '0 0 24 24',
    fill: 'none',
    stroke: 'currentColor',
    strokeWidth: '2',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    className: className
  },
  React.createElement('rect', { width: '20', height: '14', x: '2', y: '5', rx: '2' }),
  React.createElement('rect', { width: '8', height: '8', x: '8', y: '8', rx: '1' }),
  React.createElement('path', { d: 'M10 2h4' }),
  React.createElement('path', { d: 'M12 2v3' })
  )
);

export const User: React.FC<IconProps> = ({ size = 24, className }) => (
  React.createElement('svg', {
    width: size,
    height: size,
    viewBox: '0 0 24 24',
    fill: 'none',
    stroke: 'currentColor',
    strokeWidth: '2',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    className: className
  },
  React.createElement('path', { d: 'M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2' }),
  React.createElement('circle', { cx: '12', cy: '7', r: '4' })
  )
);

export const Send: React.FC<IconProps> = ({ size = 24, className }) => (
  React.createElement('svg', {
    width: size,
    height: size,
    viewBox: '0 0 24 24',
    fill: 'none',
    stroke: 'currentColor',
    strokeWidth: '2',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    className: className
  },
  React.createElement('path', { d: 'm22 2-7 20-4-9-9-4Z' }),
  React.createElement('path', { d: 'M22 2 11 13' })
  )
);

export const Mic: React.FC<IconProps> = ({ size = 24, className }) => (
  React.createElement('svg', {
    width: size,
    height: size,
    viewBox: '0 0 24 24',
    fill: 'none',
    stroke: 'currentColor',
    strokeWidth: '2',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    className: className
  },
  React.createElement('path', { d: 'M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z' }),
  React.createElement('path', { d: 'M19 10v2a7 7 0 0 1-14 0v-2' }),
  React.createElement('line', { x1: '12', x2: '12', y1: '19', y2: '22' })
  )
);

export const Paperclip: React.FC<IconProps> = ({ size = 24, className }) => (
  React.createElement('svg', {
    width: size,
    height: size,
    viewBox: '0 0 24 24',
    fill: 'none',
    stroke: 'currentColor',
    strokeWidth: '2',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    className: className
  },
  React.createElement('path', { d: 'm21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48' })
  )
);

export const MoreHorizontal: React.FC<IconProps> = ({ size = 24, className }) => (
  React.createElement('svg', {
    width: size,
    height: size,
    viewBox: '0 0 24 24',
    fill: 'none',
    stroke: 'currentColor',
    strokeWidth: '2',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    className: className
  },
  React.createElement('circle', { cx: '12', cy: '12', r: '1' }),
  React.createElement('circle', { cx: '19', cy: '12', r: '1' }),
  React.createElement('circle', { cx: '5', cy: '12', r: '1' })
  )
);

export const Settings: React.FC<IconProps> = ({ size = 24, className }) => (
  React.createElement('svg', {
    width: size,
    height: size,
    viewBox: '0 0 24 24',
    fill: 'none',
    stroke: 'currentColor',
    strokeWidth: '2',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    className: className
  },
  React.createElement('path', { d: 'M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z' }),
  React.createElement('circle', { cx: '12', cy: '12', r: '3' })
  )
);

export const History: React.FC<IconProps> = ({ size = 24, className }) => (
  React.createElement('svg', {
    width: size,
    height: size,
    viewBox: '0 0 24 24',
    fill: 'none',
    stroke: 'currentColor',
    strokeWidth: '2',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    className: className
  },
  React.createElement('path', { d: 'M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8' }),
  React.createElement('path', { d: 'M3 3v5h5' }),
  React.createElement('path', { d: 'M12 7v5l4 2' })
  )
);

export const Sparkles: React.FC<IconProps> = ({ size = 24, className }) => (
  React.createElement('svg', {
    width: size,
    height: size,
    viewBox: '0 0 24 24',
    fill: 'none',
    stroke: 'currentColor',
    strokeWidth: '2',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    className: className
  },
  React.createElement('path', { d: 'm12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z' }),
  React.createElement('path', { d: 'M5 3v4' }),
  React.createElement('path', { d: 'M19 17v4' }),
  React.createElement('path', { d: 'M3 5h4' }),
  React.createElement('path', { d: 'M17 19h4' })
  )
);

export const Brain: React.FC<IconProps> = ({ size = 24, className }) => (
  React.createElement('svg', {
    width: size,
    height: size,
    viewBox: '0 0 24 24',
    fill: 'none',
    stroke: 'currentColor',
    strokeWidth: '2',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    className: className
  },
  React.createElement('path', { d: 'M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z' }),
  React.createElement('path', { d: 'M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z' }),
  React.createElement('path', { d: 'M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4' }),
  React.createElement('path', { d: 'M17.599 6.5a3 3 0 0 0 .399-1.375' }),
  React.createElement('path', { d: 'M6.003 5.125A3 3 0 0 0 6.401 6.5' }),
  React.createElement('path', { d: 'M3.477 10.896a4 4 0 0 1 .585-.396' }),
  React.createElement('path', { d: 'M19.938 10.5a4 4 0 0 1 .585.396' }),
  React.createElement('path', { d: 'M6 18a4 4 0 0 1-1.967-.516' }),
  React.createElement('path', { d: 'M19.967 17.484A4 4 0 0 1 18 18' })
  )
);
