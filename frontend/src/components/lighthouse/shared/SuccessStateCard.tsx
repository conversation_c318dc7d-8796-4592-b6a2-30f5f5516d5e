import React from 'react';
import { motion } from 'framer-motion';
import { cn, typography, layout, animations } from '~/lib/ui-utils';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { SuccessFlipWrapper } from '~/components/ui/SuccessFlipWrapper';
import { CheckCircleIcon, PartyPopperIcon, SparklesIcon } from './icons';
import { AccessibilityUtils } from '@/utils/accessibilityUtils';

interface SuccessStateCardProps {
  title?: string;
  message?: string;
  variant?: 'default' | 'celebration' | 'achievement';
  showAction?: boolean;
  actionLabel?: string;
  onAction?: () => void;
  className?: string;
  animated?: boolean;
  autoFlip?: boolean;
  flipDelay?: number;
  details?: string;
}

export function SuccessStateCard({
  title = 'Success!',
  message = 'Operation completed successfully.',
  variant = 'default',
  showAction = false,
  actionLabel = 'Continue',
  onAction,
  className,
  animated = true,
  autoFlip = true,
  flipDelay = 500,
  details,
}: SuccessStateCardProps) {
  const prefersReducedMotion = AccessibilityUtils.prefersReducedMotion();
  const [shouldFlip, setShouldFlip] = React.useState(false);

  React.useEffect(() => {
    if (autoFlip && animated && !prefersReducedMotion) {
      const timer = setTimeout(() => {
        setShouldFlip(true);
      }, flipDelay);
      return () => clearTimeout(timer);
    }
  }, [autoFlip, animated, flipDelay, prefersReducedMotion]);

  const getVariantConfig = () => {
    switch (variant) {
      case 'celebration':
        return {
          bg: 'bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950',
          text: 'text-green-700 dark:text-green-300',
          icon: <PartyPopperIcon size={24} className="text-green-500" />,
          border: 'border-green-200 dark:border-green-800',
          shadow: 'shadow-green-200 dark:shadow-green-900',
        };
      case 'achievement':
        return {
          bg: 'bg-gradient-to-br from-yellow-50 to-amber-50 dark:from-yellow-950 dark:to-amber-950',
          text: 'text-yellow-700 dark:text-yellow-300',
          icon: <SparklesIcon size={24} className="text-yellow-500" />,
          border: 'border-yellow-200 dark:border-yellow-800',
          shadow: 'shadow-yellow-200 dark:shadow-yellow-900',
        };
      default:
        return {
          bg: 'bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900',
          text: 'text-green-700 dark:text-green-300',
          icon: <CheckCircleIcon size={24} className="text-green-500" />,
          border: 'border-green-200 dark:border-green-800',
          shadow: 'shadow-green-200 dark:shadow-green-900',
        };
    }
  };

  const config = getVariantConfig();

  const cardContent = (
    <Card className={cn(
      'w-full max-w-md border-2',
      config.bg,
      config.border,
      config.shadow,
      animated && animations.liftOnHover,
      'transition-all duration-300',
      className
    )}>
      <CardHeader className="pb-3">
        <CardTitle className={cn(
          typography.h4,
          'flex items-center gap-3',
          config.text
        )}>
          {config.icon}
          <span>{title}</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className={cn(layout.spaceY.md)}>
        <p className={cn(typography.body, config.text)}>
          {message}
        </p>
        
        {details && (
          <div className={cn(
            'mt-3 p-3 rounded-md bg-white/50 dark:bg-black/20',
            typography.bodySmall,
            config.text
          )}>
            {details}
          </div>
        )}
        
        {showAction && (
          <div className={cn(layout.flexStart, 'pt-2')}>
            <Button
              variant="default"
              size="sm"
              onClick={onAction}
              className={cn(
                'transition-all duration-200',
                animations.scaleOnHover,
                'bg-green-600 hover:bg-green-700 text-white',
                'shadow-lg hover:shadow-xl'
              )}
            >
              {actionLabel}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );

  if (animated && !prefersReducedMotion) {
    return (
      <SuccessFlipWrapper
        isSuccess={shouldFlip}
        flipAxis="y"
        duration={0.8}
        className={className}
        onAnimationComplete={() => {
          // Optional callback after flip animation
        }}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.4, ease: 'easeOut' }}
        >
          {cardContent}
        </motion.div>
      </SuccessFlipWrapper>
    );
  }

  // Fallback for reduced motion
  if (animated && prefersReducedMotion) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className={className}
      >
        {cardContent}
      </motion.div>
    );
  }

  return cardContent;
}

// Hook for managing success states
export function useSuccessState() {
  const [success, setSuccess] = React.useState<{
    title?: string;
    message?: string;
    variant?: 'default' | 'celebration' | 'achievement';
    details?: string;
  } | null>(null);

  const showSuccess = React.useCallback((successConfig: {
    title?: string;
    message?: string;
    variant?: 'default' | 'celebration' | 'achievement';
    details?: string;
  }) => {
    setSuccess(successConfig);
  }, []);

  const clearSuccess = React.useCallback(() => {
    setSuccess(null);
  }, []);

  const triggerSuccess = React.useCallback((
    successConfig: {
      title?: string;
      message?: string;
      variant?: 'default' | 'celebration' | 'achievement';
      details?: string;
    },
    duration: number = 3000
  ) => {
    setSuccess(successConfig);
    setTimeout(() => setSuccess(null), duration);
  }, []);

  return {
    success,
    showSuccess,
    clearSuccess,
    triggerSuccess,
    hasSuccess: !!success,
  };
}
