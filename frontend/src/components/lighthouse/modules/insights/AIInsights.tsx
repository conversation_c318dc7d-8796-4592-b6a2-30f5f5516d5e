import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '~/components/ui/tabs';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Progress } from '~/components/ui/progress';
import {
  Lightbulb,
  Brain,
  TrendingUp,
  Target,
  AlertCircle,
  CheckCircle,
  Clock,
  Star,
  Zap,
  ArrowRight,
  ThumbsUp,
  ThumbsDown,
  Bookmark,
  Share,
  RefreshCw,
  Filter,
  Search,
  Plus,
  Eye,
  MoreHorizontal,
  Sparkles,
  MessageSquare,
  FileText,
  Bot,
  Users,
  Calendar,
} from 'lucide-react';
import { cn } from '~/lib/utils';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';
import { format, formatDistanceToNow } from 'date-fns';

interface AIInsight {
  id: string;
  title: string;
  description: string;
  type: 'optimization' | 'recommendation' | 'prediction' | 'anomaly' | 'opportunity';
  priority: 'high' | 'medium' | 'low';
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  source: string;
  createdAt: Date;
  status: 'new' | 'viewed' | 'applied' | 'dismissed';
  actionable: boolean;
  category: string;
  metrics?: {
    expectedImpact?: string;
    timeToImplement?: string;
    complexity?: 'low' | 'medium' | 'high';
  };
  relatedItems?: string[];
}

interface Recommendation {
  id: string;
  title: string;
  description: string;
  category: 'workflow' | 'knowledge' | 'automation' | 'collaboration';
  urgency: 'high' | 'medium' | 'low';
  estimatedBenefit: string;
  implementation: string[];
  confidence: number;
}

export function AIInsights() {
  const [activeTab, setActiveTab] = useState('insights');
  const [filterType, setFilterType] = useState('all');
  const [filterPriority, setFilterPriority] = useState('all');
  
  const {
    currentProject,
    projectIntelligence,
    knowledgeCollections,
    activeAgents,
    updateProjectIntelligence,
  } = useLighthouseStore();

  // Mock insights data - would be generated by AI analysis
  const [insights, setInsights] = useState<AIInsight[]>([
    {
      id: '1',
      title: 'Knowledge Gap in API Documentation',
      description: 'Your project lacks comprehensive API documentation. Based on your research patterns, adding detailed API docs could improve development efficiency by 40%.',
      type: 'recommendation',
      priority: 'high',
      confidence: 92,
      impact: 'high',
      source: 'Knowledge Analysis',
      createdAt: new Date(Date.now() - 3600000),
      status: 'new',
      actionable: true,
      category: 'Knowledge Management',
      metrics: {
        expectedImpact: '+40% dev efficiency',
        timeToImplement: '2-3 days',
        complexity: 'medium'
      },
      relatedItems: ['API Research', 'Development Docs']
    },
    {
      id: '2',
      title: 'Optimize Agent Task Scheduling',
      description: 'Your agents are experiencing 23% idle time during peak hours. Implementing smart scheduling could increase throughput by 30%.',
      type: 'optimization',
      priority: 'medium',
      confidence: 87,
      impact: 'medium',
      source: 'Agent Analytics',
      createdAt: new Date(Date.now() - 7200000),
      status: 'viewed',
      actionable: true,
      category: 'Automation',
      metrics: {
        expectedImpact: '+30% throughput',
        timeToImplement: '1-2 hours',
        complexity: 'low'
      }
    },
    {
      id: '3',
      title: 'Unusual Research Pattern Detected',
      description: 'Your recent research sessions show 65% more security-related queries. This might indicate a shift in project focus toward security implementation.',
      type: 'anomaly',
      priority: 'low',
      confidence: 78,
      impact: 'low',
      source: 'Pattern Analysis',
      createdAt: new Date(Date.now() - 14400000),
      status: 'viewed',
      actionable: false,
      category: 'Research Patterns'
    },
    {
      id: '4',
      title: 'High-Value Source Underutilized',
      description: 'Your "Security Best Practices" collection has 89% relevance to current queries but only 12% usage. Consider promoting this source.',
      type: 'opportunity',
      priority: 'medium',
      confidence: 95,
      impact: 'medium',
      source: 'Usage Analytics',
      createdAt: new Date(Date.now() - 21600000),
      status: 'new',
      actionable: true,
      category: 'Knowledge Optimization'
    }
  ]);

  const recommendations: Recommendation[] = [
    {
      id: '1',
      title: 'Implement Smart Knowledge Tagging',
      description: 'Automatically tag and categorize your knowledge items based on content analysis and usage patterns.',
      category: 'knowledge',
      urgency: 'medium',
      estimatedBenefit: '25% faster knowledge discovery',
      implementation: [
        'Enable auto-tagging in Knowledge Hub',
        'Review and approve suggested tags',
        'Set up tag-based notifications'
      ],
      confidence: 88
    },
    {
      id: '2',
      title: 'Create Agent Templates for Common Tasks',
      description: 'Your most frequent agent tasks could be templated to reduce setup time and improve consistency.',
      category: 'automation',
      urgency: 'high',
      estimatedBenefit: '50% faster agent deployment',
      implementation: [
        'Analyze top 5 recurring agent tasks',
        'Create reusable templates',
        'Set up template sharing'
      ],
      confidence: 92
    },
    {
      id: '3',
      title: 'Establish Daily Research Workflow',
      description: 'Based on your patterns, a structured daily research routine could improve information retention by 35%.',
      category: 'workflow',
      urgency: 'low',
      estimatedBenefit: '35% better information retention',
      implementation: [
        'Set up daily research blocks',
        'Create research note templates',
        'Implement spaced repetition reviews'
      ],
      confidence: 76
    }
  ];

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'optimization':
        return <TrendingUp className="h-4 w-4" />;
      case 'recommendation':
        return <Lightbulb className="h-4 w-4" />;
      case 'prediction':
        return <Brain className="h-4 w-4" />;
      case 'anomaly':
        return <AlertCircle className="h-4 w-4" />;
      case 'opportunity':
        return <Target className="h-4 w-4" />;
      default:
        return <Sparkles className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-700 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-700 border-green-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'optimization':
        return 'bg-blue-100 text-blue-700';
      case 'recommendation':
        return 'bg-purple-100 text-purple-700';
      case 'prediction':
        return 'bg-indigo-100 text-indigo-700';
      case 'anomaly':
        return 'bg-orange-100 text-orange-700';
      case 'opportunity':
        return 'bg-emerald-100 text-emerald-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const handleInsightAction = (insightId: string, action: 'apply' | 'dismiss' | 'bookmark') => {
    setInsights(prev => prev.map(insight => 
      insight.id === insightId 
        ? { ...insight, status: action === 'apply' ? 'applied' : action === 'dismiss' ? 'dismissed' : insight.status }
        : insight
    ));
  };

  const filteredInsights = insights.filter(insight => {
    const matchesType = filterType === 'all' || insight.type === filterType;
    const matchesPriority = filterPriority === 'all' || insight.priority === filterPriority;
    return matchesType && matchesPriority;
  });

  if (!currentProject) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <Lightbulb className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No Project Selected</h3>
          <p className="text-muted-foreground">
            Select or create a project to view AI insights.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="flex h-full">
      {/* Left Sidebar - Insights Overview */}
      <aside className="w-80 border-r bg-muted/50 p-6 space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Insights
          </h3>
          
          {/* Insight Stats */}
          <div className="grid grid-cols-1 gap-3">
            <Card className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">{insights.length}</div>
                  <p className="text-xs text-muted-foreground">Total Insights</p>
                </div>
                <Lightbulb className="h-6 w-6 text-yellow-500" />
              </div>
            </Card>
            
            <Card className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {insights.filter(i => i.status === 'new').length}
                  </div>
                  <p className="text-xs text-muted-foreground">New</p>
                </div>
                <Sparkles className="h-6 w-6 text-blue-500" />
              </div>
            </Card>
            
            <Card className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {insights.filter(i => i.actionable).length}
                  </div>
                  <p className="text-xs text-muted-foreground">Actionable</p>
                </div>
                <Target className="h-6 w-6 text-green-500" />
              </div>
            </Card>
            
            <Card className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {Math.round(insights.reduce((acc, i) => acc + i.confidence, 0) / insights.length)}%
                  </div>
                  <p className="text-xs text-muted-foreground">Avg Confidence</p>
                </div>
                <Brain className="h-6 w-6 text-purple-500" />
              </div>
            </Card>
          </div>

          {/* Filters */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Filters</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-xs font-medium text-muted-foreground">Type</label>
                <select 
                  value={filterType} 
                  onChange={(e) => setFilterType(e.target.value)}
                  className="w-full mt-1 text-sm border rounded p-1"
                >
                  <option value="all">All Types</option>
                  <option value="optimization">Optimization</option>
                  <option value="recommendation">Recommendation</option>
                  <option value="prediction">Prediction</option>
                  <option value="anomaly">Anomaly</option>
                  <option value="opportunity">Opportunity</option>
                </select>
              </div>
              
              <div>
                <label className="text-xs font-medium text-muted-foreground">Priority</label>
                <select 
                  value={filterPriority} 
                  onChange={(e) => setFilterPriority(e.target.value)}
                  className="w-full mt-1 text-sm border rounded p-1"
                >
                  <option value="all">All Priorities</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                </select>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button size="sm" className="w-full justify-start">
                <RefreshCw className="h-4 w-4 mr-2" />
                Generate Insights
              </Button>
              <Button size="sm" variant="outline" className="w-full justify-start">
                <Star className="h-4 w-4 mr-2" />
                View Favorites
              </Button>
              <Button size="sm" variant="outline" className="w-full justify-start">
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Review
              </Button>
            </CardContent>
          </Card>

          {/* Intelligence Level */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Brain className="h-4 w-4" />
                Intelligence Level
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Project Understanding</span>
                  <span>{projectIntelligence?.learningLevel || 67}%</span>
                </div>
                <Progress value={projectIntelligence?.learningLevel || 67} className="h-2" />
                <p className="text-xs text-muted-foreground">
                  Insights improve as the system learns your patterns
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </aside>

      {/* Main Content */}
      <main className="flex-1 p-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">AI Insights & Recommendations</h2>
              <p className="text-muted-foreground">
                Intelligent analysis and suggestions for {currentProject.name}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
              <Button>
                <RefreshCw className="h-4 w-4 mr-2" />
                Generate New
              </Button>
            </div>
          </div>

          {/* Insights Tabs */}
          <Tabs defaultValue="insights" value={activeTab} onChange={(value) => {
            if (value) setActiveTab(value);
          }}>
            <TabsList>
              <TabsTrigger value="insights">
                AI Insights ({filteredInsights.length})
              </TabsTrigger>
              <TabsTrigger value="recommendations">
                Recommendations ({recommendations.length})
              </TabsTrigger>
              <TabsTrigger value="trends">
                Trends & Patterns
              </TabsTrigger>
            </TabsList>

            <TabsContent value="insights" className="mt-6">
              <div className="space-y-4">
                {filteredInsights.map((insight) => (
                  <Card key={insight.id} className={cn(
                    "border-l-4",
                    insight.priority === 'high' ? 'border-l-red-500' :
                    insight.priority === 'medium' ? 'border-l-yellow-500' : 'border-l-green-500'
                  )}>
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        {/* Header */}
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-3">
                            <div className={cn(
                              "p-2 rounded-lg",
                              getTypeColor(insight.type)
                            )}>
                              {getInsightIcon(insight.type)}
                            </div>
                            <div className="flex-1">
                              <h3 className="font-semibold mb-1">{insight.title}</h3>
                              <p className="text-sm text-muted-foreground mb-2">
                                {insight.description}
                              </p>
                              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                <span>{insight.source}</span>
                                <span>•</span>
                                <Clock className="h-3 w-3" />
                                <span>{insight.timestamp ? formatDistanceToNow(new Date(insight.timestamp)) : 'Unknown time'} ago</span>
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className={getPriorityColor(insight.priority)}>
                              {insight.priority}
                            </Badge>
                            <Button size="sm" variant="ghost">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        {/* Metrics */}
                        {insight.metrics && (
                          <div className="flex items-center gap-6 text-sm">
                            <div className="flex items-center gap-1">
                              <TrendingUp className="h-3 w-3 text-green-500" />
                              <span className="text-muted-foreground">Impact:</span>
                              <span className="font-medium">{insight.metrics.expectedImpact}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3 text-blue-500" />
                              <span className="text-muted-foreground">Time:</span>
                              <span className="font-medium">{insight.metrics.timeToImplement}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Target className="h-3 w-3 text-purple-500" />
                              <span className="text-muted-foreground">Confidence:</span>
                              <span className="font-medium">{insight.confidence}%</span>
                            </div>
                          </div>
                        )}

                        {/* Related Items */}
                        {insight.relatedItems && insight.relatedItems.length > 0 && (
                          <div>
                            <p className="text-sm text-muted-foreground mb-2">Related to:</p>
                            <div className="flex gap-2">
                              {insight.relatedItems.map((item, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {item}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Actions */}
                        <div className="flex items-center gap-2 pt-2 border-t border-border/50">
                          {insight.actionable && (
                            <>
                              <Button 
                                size="sm"
                                onClick={() => handleInsightAction(insight.id, 'apply')}
                                disabled={insight.status === 'applied'}
                              >
                                <CheckCircle className="h-3 w-3 mr-1" />
                                {insight.status === 'applied' ? 'Applied' : 'Apply'}
                              </Button>
                              <Button 
                                size="sm" 
                                variant="outline"
                                onClick={() => handleInsightAction(insight.id, 'bookmark')}
                              >
                                <Bookmark className="h-3 w-3 mr-1" />
                                Save
                              </Button>
                            </>
                          )}
                          <Button size="sm" variant="outline">
                            <Share className="h-3 w-3 mr-1" />
                            Share
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleInsightAction(insight.id, 'dismiss')}
                          >
                            <ThumbsDown className="h-3 w-3 mr-1" />
                            Dismiss
                          </Button>
                          <Button size="sm" variant="ghost">
                            <ThumbsUp className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}

                {filteredInsights.length === 0 && (
                  <Card>
                    <CardContent className="text-center py-12">
                      <Lightbulb className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">No Insights Found</h3>
                      <p className="text-muted-foreground mb-4">
                        No insights match your current filters, or new insights are being generated.
                      </p>
                      <Button>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Generate Insights
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </div>
            </TabsContent>

            <TabsContent value="recommendations" className="mt-6">
              <div className="space-y-4">
                {recommendations.map((rec) => (
                  <Card key={rec.id}>
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="font-semibold mb-2">{rec.title}</h3>
                            <p className="text-sm text-muted-foreground mb-3">
                              {rec.description}
                            </p>
                            <div className="flex items-center gap-4 text-sm">
                              <Badge variant="outline" className="capitalize">
                                {rec.category}
                              </Badge>
                              <span className="text-muted-foreground">
                                Benefit: <span className="font-medium">{rec.estimatedBenefit}</span>
                              </span>
                              <span className="text-muted-foreground">
                                Confidence: <span className="font-medium">{rec.confidence}%</span>
                              </span>
                            </div>
                          </div>
                          <Badge 
                            variant="outline" 
                            className={
                              rec.urgency === 'high' ? 'bg-red-50 text-red-700 border-red-200' :
                              rec.urgency === 'medium' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :
                              'bg-green-50 text-green-700 border-green-200'
                            }
                          >
                            {rec.urgency} urgency
                          </Badge>
                        </div>

                        <div>
                          <p className="text-sm font-medium mb-2">Implementation Steps:</p>
                          <ol className="text-sm space-y-1">
                            {rec.implementation.map((step, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <span className="text-muted-foreground">{index + 1}.</span>
                                <span>{step}</span>
                              </li>
                            ))}
                          </ol>
                        </div>

                        <div className="flex items-center gap-2 pt-2 border-t border-border/50">
                          <Button size="sm">
                            <Zap className="h-3 w-3 mr-1" />
                            Implement
                          </Button>
                          <Button size="sm" variant="outline">
                            <Eye className="h-3 w-3 mr-1" />
                            Learn More
                          </Button>
                          <Button size="sm" variant="outline">
                            <Calendar className="h-3 w-3 mr-1" />
                            Schedule
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="trends" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Trends & Pattern Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center text-muted-foreground">
                    <div className="text-center">
                      <TrendingUp className="h-8 w-8 mx-auto mb-2" />
                      <p>Trend analysis and pattern insights would be displayed here</p>
                      <p className="text-sm">Track behavior patterns, usage trends, and predictive insights</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
}