import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '~/components/ui/tabs';
import { ScrollArea } from '~/components/ui/scroll-area';
import {
  Microscope,
  FileText,
  MessageCircle,
  Volume2,
  Search,
  Plus,
  BookOpen,
  Quote,
  Link2,
  Sparkles,
  CheckCircle,
  AlertTriangle,
  Play,
  Download,
} from 'lucide-react';
import { cn } from '~/lib/utils';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';
import { SourceGroundedChat } from './SourceGroundedChat';
import { MultiDocAnalysis } from './MultiDocAnalysis';
import { AudioOverview } from './AudioOverview';
import { ResearchNotebook } from './ResearchNotebook';

export function ResearchStudio() {
  const [activeTab, setActiveTab] = useState('chat');
  const [selectedSources, setSelectedSources] = useState<string[]>([]);
  
  const {
    currentProject,
    knowledgeSources,
    insights,
    navigateToModule,
  } = useLighthouseStore();

  if (!currentProject) return null;

  // Research metrics
  const verifiedSources = knowledgeSources.filter(s => s.metadata.credibility > 0.8).length;
  const citedInsights = insights.filter(i => i.sources && i.sources.length > 0).length;
  const recentResearch = knowledgeSources.filter(
    s => new Date(s.metadata.date || 0) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  ).length;

  return (
    <div className="flex h-full">
      {/* Left Sidebar - Research Overview */}
      <aside className="w-80 border-r bg-muted/50 p-6 space-y-6">
        {/* Research Stats */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Microscope className="h-5 w-5" />
            Research Overview
          </h3>
          
          <div className="grid grid-cols-1 gap-3">
            <Card className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">{knowledgeSources.length}</div>
                  <p className="text-xs text-muted-foreground">Total Sources</p>
                </div>
                <FileText className="h-6 w-6 text-blue-500" />
              </div>
            </Card>
            
            <Card className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">{verifiedSources}</div>
                  <p className="text-xs text-muted-foreground">Verified Sources</p>
                </div>
                <CheckCircle className="h-6 w-6 text-green-500" />
              </div>
            </Card>
            
            <Card className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">{citedInsights}</div>
                  <p className="text-xs text-muted-foreground">Cited Insights</p>
                </div>
                <Quote className="h-6 w-6 text-purple-500" />
              </div>
            </Card>
            
            <Card className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">+{recentResearch}</div>
                  <p className="text-xs text-muted-foreground">This Week</p>
                </div>
                <Sparkles className="h-6 w-6 text-yellow-500" />
              </div>
            </Card>
          </div>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Quick Research</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button 
              size="sm" 
              className="w-full justify-start"
              onClick={() => navigateToModule('sources')}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Sources
            </Button>
            <Button size="sm" variant="outline" className="w-full justify-start">
              <Search className="h-4 w-4 mr-2" />
              Search Literature
            </Button>
            <Button size="sm" variant="outline" className="w-full justify-start">
              <Volume2 className="h-4 w-4 mr-2" />
              Generate Audio
            </Button>
            <Button size="sm" variant="outline" className="w-full justify-start">
              <Download className="h-4 w-4 mr-2" />
              Export Research
            </Button>
          </CardContent>
        </Card>

        {/* Source Quality */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Source Quality
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">High Credibility</span>
                <Badge variant="default">{verifiedSources}</Badge>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Needs Review</span>
                <Badge variant="secondary">
                  {knowledgeSources.filter(s => s.metadata.credibility < 0.6).length}
                </Badge>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Cited Sources</span>
                <Badge variant="outline">
                  {knowledgeSources.filter(s => s.metadata.citations.length > 0).length}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Citations */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Quote className="h-4 w-4" />
              Recent Citations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-32">
              <div className="space-y-2">
                {knowledgeSources
                  .filter(s => s.metadata.citations.length > 0)
                  .slice(0, 3)
                  .map((source) => (
                    <div
                      key={source.id}
                      className="text-sm p-2 rounded-lg bg-muted hover:bg-muted/80 cursor-pointer"
                    >
                      <p className="font-medium line-clamp-1">{source.metadata.title}</p>
                      <p className="text-xs text-muted-foreground">
                        {source.metadata.citations.length} citations
                      </p>
                    </div>
                  ))}
                {knowledgeSources.filter(s => s.metadata.citations.length > 0).length === 0 && (
                  <p className="text-sm text-muted-foreground">
                    No citations found yet
                  </p>
                )}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </aside>

      {/* Main Content */}
      <main className="flex-1 p-6">
        <Tabs defaultValue="chat" value={activeTab} onChange={(value) => {
          if (value) setActiveTab(value);
        }}>
          <TabsList className="mb-6">
            <TabsTrigger value="chat" className="flex items-center gap-2">
              <MessageCircle className="h-4 w-4" />
              Source-Grounded Chat
            </TabsTrigger>
            <TabsTrigger value="analysis" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Multi-Doc Analysis
            </TabsTrigger>
            <TabsTrigger value="notebook" className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Research Notebook
            </TabsTrigger>
            <TabsTrigger value="audio" className="flex items-center gap-2">
              <Volume2 className="h-4 w-4" />
              Audio Overview
            </TabsTrigger>
          </TabsList>

          <TabsContent value="chat" className="mt-0">
            <SourceGroundedChat />
          </TabsContent>

          <TabsContent value="analysis" className="mt-0">
            <MultiDocAnalysis />
          </TabsContent>

          <TabsContent value="notebook" className="mt-0">
            <ResearchNotebook />
          </TabsContent>

          <TabsContent value="audio" className="mt-0">
            <AudioOverview />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}