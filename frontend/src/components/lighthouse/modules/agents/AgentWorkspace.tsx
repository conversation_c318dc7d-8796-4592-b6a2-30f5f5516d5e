import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Progress } from '~/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { ScrollArea } from '~/components/ui/scroll-area';
import {
  Bot,
  Plus,
  Play,
  Pause,
  Square,
  Clock,
  CheckCircle,
  AlertCircle,
  Settings,
  Monitor,
  Zap,
  Target,
  Brain,
  Activity,
} from 'lucide-react';
import { cn } from '~/lib/utils';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';
import { AgentDeployment } from './AgentDeployment';
import { ProcessMonitor } from './ProcessMonitor';
import { AgentTemplates } from './AgentTemplates';

export function AgentWorkspace() {
  const [activeTab, setActiveTab] = useState('active');
  const {
    currentProject,
    activeAgents,
    agentHistory,
    deployAgent,
    updateAgentStatus,
  } = useLighthouseStore();

  if (!currentProject) return null;

  // Agent metrics
  const runningAgents = activeAgents.filter(a => a.status === 'running').length;
  const completedToday = agentHistory.filter(a => 
    a.status === 'completed' && 
    new Date().toDateString() === new Date().toDateString() // Today's completed agents
  ).length;
  const successRate = agentHistory.length > 0 
    ? (agentHistory.filter(a => a.status === 'completed').length / agentHistory.length) * 100
    : 0;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Play className="h-4 w-4 text-blue-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'bg-blue-50 border-blue-200 text-blue-700';
      case 'completed':
        return 'bg-green-50 border-green-200 text-green-700';
      case 'failed':
        return 'bg-red-50 border-red-200 text-red-700';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-700';
    }
  };

  const handleStopAgent = (agentId: string) => {
    updateAgentStatus(agentId, 'completed');
  };

  const handleRestartAgent = async (agent: any) => {
    const newAgent = {
      ...agent,
      name: `${agent.name} (Restarted)`,
    };
    delete newAgent.id;
    await deployAgent(newAgent);
  };

  return (
    <div className="flex h-full">
      {/* Left Sidebar - Agent Overview */}
      <aside className="w-80 border-r bg-muted/50 p-6 space-y-6">
        {/* Agent Stats */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Bot className="h-5 w-5" />
            Agent Workspace
          </h3>
          
          <div className="grid grid-cols-1 gap-3">
            <Card className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">{runningAgents}</div>
                  <p className="text-xs text-muted-foreground">Running Now</p>
                </div>
                <Activity className="h-6 w-6 text-blue-500" />
              </div>
            </Card>
            
            <Card className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">{completedToday}</div>
                  <p className="text-xs text-muted-foreground">Completed Today</p>
                </div>
                <CheckCircle className="h-6 w-6 text-green-500" />
              </div>
            </Card>
            
            <Card className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">{Math.round(successRate)}%</div>
                  <p className="text-xs text-muted-foreground">Success Rate</p>
                </div>
                <Target className="h-6 w-6 text-purple-500" />
              </div>
            </Card>
            
            <Card className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">{agentHistory.length + activeAgents.length}</div>
                  <p className="text-xs text-muted-foreground">Total Deployed</p>
                </div>
                <Bot className="h-6 w-6 text-gray-500" />
              </div>
            </Card>
          </div>
        </div>

        {/* Quick Deploy */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Quick Deploy</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button size="sm" className="w-full justify-start">
              <Brain className="h-4 w-4 mr-2" />
              Research Agent
            </Button>
            <Button size="sm" variant="outline" className="w-full justify-start">
              <Monitor className="h-4 w-4 mr-2" />
              Monitoring Agent
            </Button>
            <Button size="sm" variant="outline" className="w-full justify-start">
              <Zap className="h-4 w-4 mr-2" />
              Analysis Agent
            </Button>
            <Button size="sm" variant="outline" className="w-full justify-start">
              <Plus className="h-4 w-4 mr-2" />
              Custom Agent
            </Button>
          </CardContent>
        </Card>

        {/* System Status */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Activity className="h-4 w-4" />
              System Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">CPU Usage</span>
                <div className="flex items-center gap-2">
                  <div className="w-16 h-1.5 bg-muted rounded-full overflow-hidden">
                    <div className="h-full bg-blue-500 rounded-full" style={{ width: '45%' }} />
                  </div>
                  <span className="text-xs">45%</span>
                </div>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Memory</span>
                <div className="flex items-center gap-2">
                  <div className="w-16 h-1.5 bg-muted rounded-full overflow-hidden">
                    <div className="h-full bg-green-500 rounded-full" style={{ width: '67%' }} />
                  </div>
                  <span className="text-xs">67%</span>
                </div>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Queue</span>
                <Badge variant="outline">{activeAgents.filter(a => a.status === 'idle').length} pending</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </aside>

      {/* Main Content */}
      <main className="flex-1 p-6">
        <Tabs defaultValue="active" value={activeTab} onChange={(value) => {
          if (value) setActiveTab(value);
        }}>
          <TabsList className="mb-6">
            <TabsTrigger value="active" className="flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Active Agents ({runningAgents})
            </TabsTrigger>
            <TabsTrigger value="deploy" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Deploy New
            </TabsTrigger>
            <TabsTrigger value="monitor" className="flex items-center gap-2">
              <Monitor className="h-4 w-4" />
              Process Monitor
            </TabsTrigger>
            <TabsTrigger value="templates" className="flex items-center gap-2">
              <Bot className="h-4 w-4" />
              Templates
            </TabsTrigger>
          </TabsList>

          <TabsContent value="active" className="mt-0">
            <div className="space-y-4">
              {/* Running Agents */}
              {activeAgents.length > 0 ? (
                <div className="space-y-4">
                  {activeAgents.map((agent) => (
                    <Card key={agent.id} className={cn("border-l-4", getStatusColor(agent.status))}>
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-3">
                            {getStatusIcon(agent.status)}
                            <div>
                              <CardTitle className="text-base">{agent.name}</CardTitle>
                              <p className="text-sm text-muted-foreground mt-1">
                                {agent.context.goal}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="capitalize">
                              {agent.type}
                            </Badge>
                            <Badge variant={agent.status === 'running' ? 'default' : 'secondary'}>
                              {agent.status}
                            </Badge>
                          </div>
                        </div>
                      </CardHeader>
                      
                      <CardContent>
                        <div className="space-y-4">
                          {/* Progress */}
                          {agent.status === 'running' && (
                            <div>
                              <div className="flex items-center justify-between text-sm mb-1">
                                <span className="text-muted-foreground">Progress</span>
                                <span>Processing...</span>
                              </div>
                              <Progress value={65} className="h-2" />
                            </div>
                          )}

                          {/* Autonomy Level */}
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">Autonomy Level</span>
                            <div className="flex items-center gap-2">
                              {[...Array(10)].map((_, i) => (
                                <div
                                  key={i}
                                  className={cn(
                                    "w-2 h-2 rounded-full",
                                    i < agent.context.autonomyLevel ? "bg-primary" : "bg-muted"
                                  )}
                                />
                              ))}
                              <span className="text-xs ml-1">{agent.context.autonomyLevel}/10</span>
                            </div>
                          </div>

                          {/* Capabilities */}
                          <div>
                            <p className="text-sm text-muted-foreground mb-2">Capabilities</p>
                            <div className="flex flex-wrap gap-1">
                              {agent.context.capabilities.map((capability, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {capability}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          {/* Results Preview */}
                          {agent.results && agent.results.length > 0 && (
                            <div>
                              <p className="text-sm text-muted-foreground mb-2">
                                Recent Results ({agent.results.length})
                              </p>
                              <div className="space-y-1">
                                {agent.results.slice(0, 2).map((result) => (
                                  <div key={result.id} className="text-xs p-2 rounded bg-muted">
                                    <span className="font-medium">{result.type}:</span> {result.content}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Actions */}
                          <div className="flex items-center gap-2 pt-2">
                            {agent.status === 'running' && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleStopAgent(agent.id)}
                              >
                                <Square className="h-3 w-3 mr-1" />
                                Stop
                              </Button>
                            )}
                            <Button size="sm" variant="ghost">
                              <Monitor className="h-3 w-3 mr-1" />
                              Monitor
                            </Button>
                            <Button size="sm" variant="ghost">
                              <Settings className="h-3 w-3 mr-1" />
                              Configure
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="text-center py-12">
                    <Bot className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Active Agents</h3>
                    <p className="text-muted-foreground mb-4 max-w-md mx-auto">
                      Deploy your first AI agent to start autonomous task execution.
                      Agents can work independently while you focus on strategic decisions.
                    </p>
                    <Button onClick={() => setActiveTab('deploy')}>
                      <Plus className="h-4 w-4 mr-2" />
                      Deploy First Agent
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          <TabsContent value="deploy" className="mt-0">
            <AgentDeployment />
          </TabsContent>

          <TabsContent value="monitor" className="mt-0">
            <ProcessMonitor />
          </TabsContent>

          <TabsContent value="templates" className="mt-0">
            <AgentTemplates />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}