import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Badge } from '~/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Progress } from '~/components/ui/progress';
import {
  FileText,
  Upload,
  Link,
  Database,
  Globe,
  Folder,
  Search,
  Filter,
  MoreHorizontal,
  Download,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
  FileIcon,
  ImageIcon,
  VideoIcon,
  Volume2,
  ArchiveIcon,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  UploadCloud,
  ExternalLink,
} from 'lucide-react';
import { cn } from '~/lib/utils';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';
import { format } from 'date-fns';

interface Source {
  id: string;
  name: string;
  type: 'file' | 'url' | 'api' | 'database';
  status: 'processing' | 'ready' | 'error';
  size?: number;
  uploadedAt: Date;
  processedAt?: Date;
  contentType: string;
  extractedData?: {
    text?: string;
    metadata?: Record<string, any>;
    entities?: string[];
    topics?: string[];
  };
  source: string;
  progress?: number;
}

export function SourceManager() {
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [dragActive, setDragActive] = useState(false);
  
  const { currentProject, addSource, removeSource, updateSource } = useLighthouseStore();

  // Mock data - would come from store in real implementation
  const [sources, setSources] = useState<Source[]>([
    {
      id: '1',
      name: 'Project Requirements.pdf',
      type: 'file',
      status: 'ready',
      size: 2048576,
      uploadedAt: new Date(Date.now() - 86400000),
      processedAt: new Date(Date.now() - 86400000 + 300000),
      contentType: 'application/pdf',
      source: '/uploads/requirements.pdf',
      extractedData: {
        text: 'Project requirements document...',
        metadata: { pages: 15, author: 'John Doe' },
        entities: ['Authentication', 'Database', 'API'],
        topics: ['Security', 'Architecture', 'Implementation']
      }
    },
    {
      id: '2',
      name: 'https://docs.example.com/api',
      type: 'url',
      status: 'processing',
      uploadedAt: new Date(Date.now() - 3600000),
      contentType: 'text/html',
      source: 'https://docs.example.com/api',
      progress: 65
    },
    {
      id: '3',
      name: 'Research Papers Collection.zip',
      type: 'file',
      status: 'error',
      size: 52428800,
      uploadedAt: new Date(Date.now() - 1800000),
      contentType: 'application/zip',
      source: '/uploads/research.zip'
    }
  ]);

  const getSourceIcon = (type: string, contentType: string) => {
    if (type === 'url') return <Globe className="h-4 w-4" />;
    if (type === 'api') return <Database className="h-4 w-4" />;
    if (type === 'database') return <Database className="h-4 w-4" />;
    
    if (contentType.includes('pdf') || contentType.includes('document')) return <FileText className="h-4 w-4" />;
    if (contentType.includes('image')) return <ImageIcon className="h-4 w-4" />;
    if (contentType.includes('video')) return <VideoIcon className="h-4 w-4" />;
    if (contentType.includes('audio')) return <Volume2 className="h-4 w-4" />;
    if (contentType.includes('zip') || contentType.includes('archive')) return <ArchiveIcon className="h-4 w-4" />;
    
    return <FileIcon className="h-4 w-4" />;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready':
        return 'text-green-500';
      case 'processing':
        return 'text-blue-500';
      case 'error':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown size';
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const files = Array.from(e.dataTransfer.files);
    files.forEach(file => {
      const newSource: Source = {
        id: Date.now().toString(),
        name: file.name,
        type: 'file',
        status: 'processing',
        size: file.size,
        uploadedAt: new Date(),
        contentType: file.type || 'application/octet-stream',
        source: URL.createObjectURL(file),
        progress: 0
      };
      
      setSources(prev => [...prev, newSource]);
      
      // Simulate processing
      simulateProcessing(newSource.id);
    });
  };

  const simulateProcessing = (sourceId: string) => {
    const interval = setInterval(() => {
      setSources(prev => prev.map(source => {
        if (source.id === sourceId) {
          const newProgress = (source.progress || 0) + Math.random() * 20;
          if (newProgress >= 100) {
            clearInterval(interval);
            return {
              ...source,
              status: 'ready',
              progress: 100,
              processedAt: new Date(),
              extractedData: {
                text: 'Extracted content...',
                metadata: { processed: true },
                entities: ['Entity1', 'Entity2'],
                topics: ['Topic1', 'Topic2']
              }
            };
          }
          return { ...source, progress: newProgress };
        }
        return source;
      }));
    }, 500);
  };

  const filteredSources = sources.filter(source => {
    const matchesSearch = source.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         source.contentType.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesTab = activeTab === 'all' || 
                      (activeTab === 'files' && source.type === 'file') ||
                      (activeTab === 'urls' && source.type === 'url') ||
                      (activeTab === 'apis' && (source.type === 'api' || source.type === 'database'));
    
    return matchesSearch && matchesTab;
  });

  if (!currentProject) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No Project Selected</h3>
          <p className="text-muted-foreground">
            Select or create a project to manage sources.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="flex h-full">
      {/* Left Sidebar - Source Stats */}
      <aside className="w-80 border-r bg-muted/50 p-6 space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Database className="h-5 w-5" />
            Source Manager
          </h3>
          
          {/* Quick Stats */}
          <div className="grid grid-cols-1 gap-3">
            <Card className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">{sources.length}</div>
                  <p className="text-xs text-muted-foreground">Total Sources</p>
                </div>
                <FileText className="h-6 w-6 text-blue-500" />
              </div>
            </Card>
            
            <Card className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {sources.filter(s => s.status === 'ready').length}
                  </div>
                  <p className="text-xs text-muted-foreground">Ready</p>
                </div>
                <CheckCircle className="h-6 w-6 text-green-500" />
              </div>
            </Card>
            
            <Card className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {sources.filter(s => s.status === 'processing').length}
                  </div>
                  <p className="text-xs text-muted-foreground">Processing</p>
                </div>
                <RefreshCw className="h-6 w-6 text-blue-500" />
              </div>
            </Card>
            
            <Card className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {Math.round(sources.reduce((total, s) => total + (s.size || 0), 0) / 1024 / 1024)}MB
                  </div>
                  <p className="text-xs text-muted-foreground">Storage Used</p>
                </div>
                <Database className="h-6 w-6 text-purple-500" />
              </div>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button size="sm" className="w-full justify-start">
                <Upload className="h-4 w-4 mr-2" />
                Upload Files
              </Button>
              <Button size="sm" variant="outline" className="w-full justify-start">
                <Link className="h-4 w-4 mr-2" />
                Add URL
              </Button>
              <Button size="sm" variant="outline" className="w-full justify-start">
                <Database className="h-4 w-4 mr-2" />
                Connect API
              </Button>
              <Button size="sm" variant="outline" className="w-full justify-start">
                <Folder className="h-4 w-4 mr-2" />
                Bulk Import
              </Button>
            </CardContent>
          </Card>

          {/* Processing Queue */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <RefreshCw className="h-4 w-4" />
                Processing Queue
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {sources.filter(s => s.status === 'processing').map(source => (
                  <div key={source.id} className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="truncate flex-1">{source.name}</span>
                      <span className="text-xs">{Math.round(source.progress || 0)}%</span>
                    </div>
                    <Progress value={source.progress || 0} className="h-1" />
                  </div>
                ))}
                {sources.filter(s => s.status === 'processing').length === 0 && (
                  <p className="text-xs text-muted-foreground">No sources processing</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </aside>

      {/* Main Content */}
      <main className="flex-1 p-6">
        {/* Header */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">Source Management</h2>
              <p className="text-muted-foreground">
                Manage files, URLs, and data sources for {currentProject.name}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2">
                <Search className="h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search sources..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-64"
                />
              </div>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Source
              </Button>
            </div>
          </div>

          {/* Upload Area */}
          <Card 
            className={cn(
              "border-2 border-dashed transition-colors",
              dragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25"
            )}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <CardContent className="text-center py-8">
              <UploadCloud className="h-8 w-8 text-muted-foreground mx-auto mb-4" />
              <h3 className="font-medium mb-2">Drop files here or click to upload</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Supports PDF, DOC, TXT, images, and more
              </p>
              <div className="flex items-center justify-center gap-2">
                <Button variant="outline">
                  <Upload className="h-4 w-4 mr-2" />
                  Browse Files
                </Button>
                <Button variant="outline">
                  <Link className="h-4 w-4 mr-2" />
                  Add URL
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Sources Tabs */}
          <Tabs defaultValue="all" value={activeTab} onChange={(value) => {
            if (value) setActiveTab(value);
          }}>
            <TabsList>
              <TabsTrigger value="all">
                All Sources ({sources.length})
              </TabsTrigger>
              <TabsTrigger value="files">
                Files ({sources.filter(s => s.type === 'file').length})
              </TabsTrigger>
              <TabsTrigger value="urls">
                URLs ({sources.filter(s => s.type === 'url').length})
              </TabsTrigger>
              <TabsTrigger value="apis">
                APIs ({sources.filter(s => s.type === 'api' || s.type === 'database').length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="mt-6">
              <div className="space-y-4">
                {filteredSources.map((source) => (
                  <Card key={source.id}>
                    <CardContent className="p-4">
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0">
                          {getSourceIcon(source.type, source.contentType)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex-1 min-w-0">
                              <h4 className="font-medium truncate">{source.name}</h4>
                              <div className="flex items-center gap-3 text-sm text-muted-foreground">
                                <span>{source.contentType}</span>
                                {source.size && <span>{formatFileSize(source.size)}</span>}
                                <span>
                                  <Clock className="h-3 w-3 inline mr-1" />
                                  {format(source.uploadedAt, 'MMM d, HH:mm')}
                                </span>
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-2">
                              <Badge 
                                variant={source.status === 'ready' ? 'default' : 'secondary'}
                                className={getStatusColor(source.status)}
                              >
                                {source.status === 'processing' && source.progress && (
                                  <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                                )}
                                {source.status === 'ready' && <CheckCircle className="h-3 w-3 mr-1" />}
                                {source.status === 'error' && <AlertCircle className="h-3 w-3 mr-1" />}
                                {source.status}
                              </Badge>
                              <Button size="sm" variant="ghost">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>

                          {/* Processing Progress */}
                          {source.status === 'processing' && source.progress !== undefined && (
                            <div className="mb-3">
                              <Progress value={source.progress} className="h-2" />
                              <p className="text-xs text-muted-foreground mt-1">
                                Processing... {Math.round(source.progress)}%
                              </p>
                            </div>
                          )}

                          {/* Extracted Data Preview */}
                          {source.extractedData && source.status === 'ready' && (
                            <div className="space-y-2">
                              <div className="flex items-center gap-4 text-sm">
                                {source.extractedData.entities && (
                                  <div className="flex items-center gap-1">
                                    <span className="text-muted-foreground">Entities:</span>
                                    <div className="flex gap-1">
                                      {source.extractedData.entities.slice(0, 3).map((entity, i) => (
                                        <Badge key={i} variant="outline" className="text-xs">
                                          {entity}
                                        </Badge>
                                      ))}
                                      {source.extractedData.entities.length > 3 && (
                                        <span className="text-xs text-muted-foreground">
                                          +{source.extractedData.entities.length - 3} more
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                )}
                              </div>
                              
                              {source.extractedData.topics && (
                                <div className="flex items-center gap-1">
                                  <span className="text-sm text-muted-foreground">Topics:</span>
                                  <div className="flex gap-1">
                                    {source.extractedData.topics.slice(0, 3).map((topic, i) => (
                                      <Badge key={i} variant="secondary" className="text-xs">
                                        {topic}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          )}

                          {/* Actions */}
                          <div className="flex items-center gap-2 mt-3">
                            <Button size="sm" variant="outline">
                              <Eye className="h-3 w-3 mr-1" />
                              Preview
                            </Button>
                            <Button size="sm" variant="outline">
                              <Download className="h-3 w-3 mr-1" />
                              Download
                            </Button>
                            {source.type === 'url' && (
                              <Button size="sm" variant="outline">
                                <ExternalLink className="h-3 w-3 mr-1" />
                                Visit
                              </Button>
                            )}
                            <Button size="sm" variant="outline">
                              <Edit className="h-3 w-3 mr-1" />
                              Edit
                            </Button>
                            <Button size="sm" variant="outline" className="text-red-600">
                              <Trash2 className="h-3 w-3 mr-1" />
                              Delete
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}

                {filteredSources.length === 0 && (
                  <Card>
                    <CardContent className="text-center py-12">
                      <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">No Sources Found</h3>
                      <p className="text-muted-foreground mb-4">
                        {searchQuery ? 'No sources match your search criteria.' : 'Add your first source to get started.'}
                      </p>
                      {!searchQuery && (
                        <Button>
                          <Plus className="h-4 w-4 mr-2" />
                          Add First Source
                        </Button>
                      )}
                    </CardContent>
                  </Card>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
}