import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { ScrollArea } from '~/components/ui/scroll-area';
import {
  Activity,
  FileText,
  Brain,
  Lightbulb,
  Bot,
  Link,
  CheckCircle,
  AlertCircle,
  Clock,
} from 'lucide-react';
import { cn } from '~/lib/utils';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';
import { formatDistanceToNow } from 'date-fns';

interface ActivityItem {
  id: string;
  type: 'insight' | 'agent' | 'knowledge' | 'source' | 'learning';
  title: string;
  description: string;
  timestamp: Date;
  status?: 'success' | 'pending' | 'error';
  icon: React.ElementType;
  module?: any;
}

export function ActivityFeed() {
  const {
    insights,
    activeAgents,
    agentHistory,
    learningEvents,
    knowledgeSources,
    currentProject,
    navigateToModule,
  } = useLighthouseStore();

  // Generate activity items from various sources
  const activities: ActivityItem[] = [];

  // Add recent insights
  insights.slice(0, 5).forEach((insight) => {
    activities.push({
      id: `insight-${insight.id}`,
      type: 'insight',
      title: 'New Insight Generated',
      description: insight.content.substring(0, 100) + '...',
      timestamp: new Date(insight.timestamp),
      status: 'success',
      icon: Lightbulb,
      module: 'insights',
    });
  });

  // Add agent activities
  [...activeAgents, ...agentHistory.slice(0, 3)].forEach((agent) => {
    activities.push({
      id: `agent-${agent.id}`,
      type: 'agent',
      title: `Agent: ${agent.name}`,
      description: agent.context.goal,
      timestamp: new Date(), // TODO: Add timestamp to agent
      status: agent.status === 'completed' ? 'success' : 
              agent.status === 'failed' ? 'error' : 'pending',
      icon: Bot,
      module: 'agents',
    });
  });

  // Add learning events
  learningEvents.slice(0, 3).forEach((event) => {
    const learningTypeLabel = {
      concept: 'New Concept',
      connection: 'Connection Found',
      pattern: 'Pattern Recognized',
      correction: 'Knowledge Corrected',
      reinforcement: 'Knowledge Reinforced',
      revision: 'Knowledge Revised'
    };
    
    activities.push({
      id: `learning-${event.id}`,
      type: 'learning',
      title: learningTypeLabel[event.type] || `Learning: ${event.type}`,
      description: event.after?.understanding || 'Knowledge updated',
      timestamp: new Date(event.timestamp),
      status: 'success',
      icon: Brain,
      module: 'knowledge',
    });
  });

  // Add recent sources
  knowledgeSources.slice(0, 3).forEach((source) => {
    activities.push({
      id: `source-${source.id}`,
      type: 'source',
      title: 'New Source Added',
      description: source.metadata.title || source.location,
      timestamp: new Date(), // TODO: Add timestamp to source
      status: 'success',
      icon: FileText,
      module: 'sources',
    });
  });

  // Sort by timestamp (ensure timestamps are Date objects)
  activities.sort((a, b) => {
    const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();
    const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();
    return timeB - timeA;
  });

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500 animate-pulse" />;
      default:
        return null;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'insight':
        return 'text-yellow-600 bg-yellow-50';
      case 'agent':
        return 'text-blue-600 bg-blue-50';
      case 'learning':
        return 'text-purple-600 bg-purple-50';
      case 'source':
        return 'text-green-600 bg-green-50';
      case 'knowledge':
        return 'text-indigo-600 bg-indigo-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Activity Feed
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[500px] pr-4">
          {activities.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No recent activity</p>
              <p className="text-sm text-muted-foreground mt-2">
                Start by adding sources or asking questions
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {activities.map((activity) => {
                const Icon = activity.icon;
                return (
                  <div
                    key={activity.id}
                    className="flex gap-3 p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors cursor-pointer"
                    onClick={() => activity.module && navigateToModule(activity.module)}
                  >
                    <div
                      className={cn(
                        "h-10 w-10 rounded-full flex items-center justify-center",
                        getActivityColor(activity.type)
                      )}
                    >
                      <Icon className="h-5 w-5" />
                    </div>
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center gap-2">
                        <p className="font-medium text-sm">{activity.title}</p>
                        {activity.status && getStatusIcon(activity.status)}
                      </div>
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {activity.description}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatDistanceToNow(activity.timestamp, { addSuffix: true })}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}