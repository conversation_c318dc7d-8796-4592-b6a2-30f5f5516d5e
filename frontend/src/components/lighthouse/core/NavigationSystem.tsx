import React from 'react';
import { useNavigate } from '@tanstack/react-router';
import { motion } from 'framer-motion';
import { cn, typography, layout, animations, states, accessibility, visualEffects } from '~/lib/ui-utils';
import { ProgressBar } from '~/components/ui/loading-states';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { AnimatedBorderWrapper } from '~/components/ui/AnimatedBorderWrapper';
import { HoverLift, Pressable, StaggerContainer, StaggerItem } from '../shared/EnhancedMicrointeractions';
import {
  HomeIcon,
  BookTextIcon,
  TelescopeIcon,
  CpuIcon,
  MessageCircleIcon,
  FileTextIcon,
  ChartLineIcon,
  SparklesIcon,
  ClockIcon,
  ActivityIcon
} from '../shared/icons';
import { useLighthouseStore } from '../shared/store/lighthouse-store';
import { ModuleName, MODULE_ROUTES } from '../types/navigation.types';

const moduleIcons: Record<ModuleName, React.ElementType> = {
  dashboard: HomeIcon,
  knowledge: BookTextIcon,
  research: TelescopeIcon,
  agents: CpuIcon,
  chat: MessageCircleIcon,
  sources: FileTextIcon,
  analytics: ChartLineIcon,
  insights: SparklesIcon,
};

export function NavigationSystem() {
  const navigate = useNavigate();
  const {
    navigation,
    currentProject,
    setCurrentModule,
    suggestions,
    activeAgents,
    knowledgeSources,
  } = useLighthouseStore();

  const handleModuleClick = (module: ModuleName) => {
    setCurrentModule(module);
    navigate({
      to: '/lighthouse',
      search: { module },
    });
  };

  // Get notification counts for modules
  const getModuleNotifications = (module: ModuleName): number => {
    switch (module) {
      case 'insights':
        return suggestions.filter(s => s.priority === 'high').length;
      case 'agents':
        return activeAgents.length;
      case 'sources':
        return knowledgeSources.filter(s => s.metadata.credibility < 0.5).length;
      default:
        return 0;
    }
  };

  return (
    <aside
      className={cn(
        "w-64 border-r flex flex-col transition-all duration-300",
        visualEffects.gradients.subtle,
        visualEffects.shadows.soft,
        "backdrop-blur-sm"
      )}
      role="navigation"
      aria-label="Main navigation"
    >
      {/* Module Navigation */}
      <nav className={cn('flex-1 p-4', layout.spaceY.sm)} aria-label="Module navigation">
        <StaggerContainer staggerDelay={0.05}>
          {Object.entries(MODULE_ROUTES).map(([key, route]) => {
          const module = key as ModuleName;
          const Icon = moduleIcons[module];
          const isActive = navigation.currentModule === module;
          const notifications = getModuleNotifications(module);
          const isDisabled = !currentProject && module !== 'dashboard';

          return (
            <StaggerItem key={module} className="relative">
              <HoverLift
                liftHeight={4}
                scale={1.02}
                duration={0.2}
                disabled={isDisabled}
              >
              {isActive && (
                <motion.div
                  layoutId="activeIndicator"
                  className="absolute left-0 top-0 bottom-0 w-1 bg-primary rounded-r-full"
                  initial={false}
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                />
              )}

              <AnimatedBorderWrapper
                isLoading={false}
                variant="pulse"
                borderColor={isActive ? "#3b82f6" : "transparent"}
                disabled={!isActive}
                className="rounded-lg"
              >
                <Button
                  variant={isActive ? 'secondary' : 'ghost'}
                  className={cn(
                    'w-full justify-start relative transition-all duration-300',
                    'group hover:bg-accent/50 focus-visible:bg-accent/50',
                    isActive && [
                      visualEffects.gradients.primary,
                      visualEffects.shadows.colored.primary,
                      'border border-primary/20 text-primary-foreground',
                      'hover:shadow-lg hover:shadow-primary/30'
                    ],
                    !isActive && [
                      states.hoverGhost,
                      animations.liftOnHover,
                      'hover:bg-accent/70 hover:shadow-md'
                    ],
                    isDisabled && 'opacity-50 cursor-not-allowed',
                    states.focusRing
                  )}
                  onClick={() => !isDisabled && handleModuleClick(module)}
                  disabled={isDisabled}
                  title={isDisabled ? 'Select a project first' : route.description}
                  aria-current={isActive ? 'page' : undefined}
                  aria-describedby={`${module}-description`}
                >
                  <motion.div
                    className="flex items-center w-full"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Icon
                      size={18}
                      className={cn(
                        'mr-3 transition-all duration-300',
                        isActive ? 'text-primary' : 'text-muted-foreground group-hover:text-foreground',
                        !isDisabled && 'group-hover:scale-110'
                      )}
                    />
                    <span className={cn(
                      typography.label,
                      'flex-1 text-left transition-all duration-300',
                      isActive && 'text-foreground font-semibold',
                      !isActive && 'group-hover:font-medium'
                    )}>
                      {route.label}
                    </span>
                    {notifications > 0 && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        whileHover={{ scale: 1.1 }}
                        transition={{ type: "spring", stiffness: 300 }}
                      >
                        <Badge
                          variant={module === 'agents' ? 'default' : 'secondary'}
                          className={cn(
                            'ml-auto h-5 px-1.5 min-w-[20px] transition-all duration-200',
                            'animate-pulse hover:animate-none',
                            module === 'agents' && 'bg-green-500 text-white',
                            module === 'insights' && 'bg-yellow-500 text-white',
                            module === 'sources' && 'bg-red-500 text-white'
                          )}
                          aria-label={`${notifications} notifications`}
                        >
                          {notifications}
                        </Badge>
                      </motion.div>
                    )}
                  </motion.div>
                  <span
                    id={`${module}-description`}
                    className={accessibility.srOnly}
                  >
                    {route.description}
                  </span>
                </Button>
              </AnimatedBorderWrapper>
              </HoverLift>
            </StaggerItem>
          );
        })}
        </StaggerContainer>
      </nav>

      {/* Intelligence Status */}
      {currentProject && (
        <motion.div
          className={cn(
            "p-4 border-t",
            visualEffects.gradients.glass,
            visualEffects.shadows.soft,
            "backdrop-blur-sm"
          )}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.3 }}
        >
          <div className={cn(layout.spaceY.sm)}>
            {/* Active Processes */}
            {activeAgents.length > 0 && (
              <motion.div
                className={cn(layout.flexStart, 'gap-2', typography.bodySmall)}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <motion.div
                  className="h-2 w-2 bg-green-500 rounded-full shadow-sm"
                  animate={{
                    scale: [1, 1.2, 1],
                    boxShadow: [
                      '0 0 0 0 rgba(34, 197, 94, 0.4)',
                      '0 0 0 4px rgba(34, 197, 94, 0)',
                      '0 0 0 0 rgba(34, 197, 94, 0)'
                    ]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                />
                <span className="text-muted-foreground">
                  {activeAgents.length} process{activeAgents.length !== 1 ? 'es' : ''} running
                </span>
              </motion.div>
            )}

            {/* High Priority Suggestions */}
            {suggestions.filter(s => s.priority === 'high').length > 0 && (
              <motion.div
                className={cn(layout.flexStart, 'gap-2', typography.bodySmall)}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                <SparklesIcon size={12} className="text-yellow-500" />
                <span className="text-muted-foreground">
                  {suggestions.filter(s => s.priority === 'high').length} insights available
                </span>
              </motion.div>
            )}

            {/* Knowledge Growth */}
            {currentProject.intelligence.learningLevel > 0 && (
              <motion.div
                className="space-y-1"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              >
                <div className={cn(layout.flexBetween, typography.caption)}>
                  <span className="text-muted-foreground flex items-center gap-1">
                    <ActivityIcon size={10} className="text-primary" />
                    Knowledge Growth
                  </span>
                  <motion.span
                    className={cn(typography.label, 'text-primary')}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                  >
                    +{Math.round(currentProject.intelligence.learningLevel / 10)}%
                  </motion.span>
                </div>
                <ProgressBar
                  value={currentProject.intelligence.learningLevel}
                  size="sm"
                  variant="success"
                  animated
                  aria-label="Knowledge growth progress"
                />
              </motion.div>
            )}
          </div>
        </motion.div>
      )}

      {/* Quick Actions */}
      <motion.div
        className={cn(
          'p-4 border-t',
          visualEffects.gradients.glass,
          visualEffects.shadows.soft,
          layout.spaceY.sm
        )}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay: 0.4 }}
      >
        <Pressable disabled={!currentProject}>
          <Button
            variant="outline"
            size="sm"
            className={cn(
              'w-full transition-all duration-300',
              visualEffects.gradients.primary,
              visualEffects.shadows.colored.primary,
              'border-primary/30 hover:border-primary/50',
              'text-primary-foreground hover:text-white',
              animations.liftOnHover,
              states.focusRing,
              !currentProject && states.disabled
            )}
            onClick={() => handleModuleClick('chat')}
            disabled={!currentProject}
            aria-label="Start a quick chat conversation"
          >
            <MessageCircleIcon size={16} className="mr-2" />
            Quick Question
          </Button>
        </Pressable>

        <Pressable disabled={!currentProject}>
          <Button
            variant="outline"
            size="sm"
            className={cn(
              'w-full transition-all duration-300',
              'bg-gradient-to-r from-secondary/10 to-secondary/20',
              'border-secondary/30 hover:border-secondary/50',
              'hover:bg-gradient-to-r hover:from-secondary/20 hover:to-secondary/30',
              visualEffects.shadows.medium,
              animations.liftOnHover,
              states.focusRing,
              !currentProject && states.disabled
            )}
            onClick={() => handleModuleClick('sources')}
            disabled={!currentProject}
            aria-label="Add a new source to the project"
          >
            <FileTextIcon size={16} className="mr-2" />
            Add Source
          </Button>
        </Pressable>
      </motion.div>
    </aside>
  );
}