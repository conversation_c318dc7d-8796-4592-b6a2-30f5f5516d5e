/**
 * UI Utility functions for consistent styling and interactions
 */

import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { designTokens } from './design-tokens';

// Enhanced cn function with design token support
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Typography utility classes
export const typography = {
  // Display text for hero sections
  display: 'text-4xl font-bold leading-tight tracking-tight',
  
  // Headings with proper hierarchy
  h1: 'text-3xl font-semibold leading-tight',
  h2: 'text-2xl font-semibold leading-tight', 
  h3: 'text-xl font-medium leading-normal',
  h4: 'text-lg font-medium leading-normal',
  h5: 'text-base font-medium leading-normal',
  h6: 'text-sm font-medium leading-normal',
  
  // Body text variants
  body: 'text-sm leading-relaxed',
  bodyLarge: 'text-base leading-relaxed',
  bodySmall: 'text-xs leading-normal',
  
  // Special purpose text
  caption: 'text-xs text-muted-foreground leading-normal',
  label: 'text-sm font-medium leading-normal',
  code: 'font-mono text-sm bg-muted px-1 py-0.5 rounded',
  
  // Interactive text
  link: 'text-primary hover:text-primary/80 underline-offset-4 hover:underline transition-colors',
  button: 'font-medium leading-none',
} as const;

// Layout utility classes
export const layout = {
  // Container variants
  container: 'mx-auto max-w-7xl px-4 sm:px-6 lg:px-8',
  containerFluid: 'w-full px-4 sm:px-6 lg:px-8',
  
  // Flex utilities
  flexCenter: 'flex items-center justify-center',
  flexBetween: 'flex items-center justify-between',
  flexStart: 'flex items-center justify-start',
  flexEnd: 'flex items-center justify-end',
  flexCol: 'flex flex-col',
  flexColCenter: 'flex flex-col items-center justify-center',
  
  // Grid utilities
  gridAuto: 'grid grid-cols-[repeat(auto-fit,minmax(280px,1fr))]',
  gridResponsive: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  
  // Spacing utilities using design tokens
  spaceY: {
    xs: 'space-y-1',
    sm: 'space-y-2', 
    md: 'space-y-4',
    lg: 'space-y-6',
    xl: 'space-y-8',
  },
  
  // Padding utilities
  padding: {
    xs: 'p-1',
    sm: 'p-2',
    md: 'p-4', 
    lg: 'p-6',
    xl: 'p-8',
  },
} as const;

// Enhanced animation utilities
export const animations = {
  // Entrance animations
  fadeIn: 'animate-in fade-in duration-200',
  slideInFromTop: 'animate-in slide-in-from-top-2 duration-200',
  slideInFromBottom: 'animate-in slide-in-from-bottom-2 duration-200',
  slideInFromLeft: 'animate-in slide-in-from-left-2 duration-200',
  slideInFromRight: 'animate-in slide-in-from-right-2 duration-200',

  // Exit animations
  fadeOut: 'animate-out fade-out duration-150',
  slideOutToTop: 'animate-out slide-out-to-top-2 duration-150',
  slideOutToBottom: 'animate-out slide-out-to-bottom-2 duration-150',
  slideOutToLeft: 'animate-out slide-out-to-left-2 duration-150',
  slideOutToRight: 'animate-out slide-out-to-right-2 duration-150',

  // Enhanced hover animations
  scaleOnHover: 'transition-transform hover:scale-105 duration-200',
  liftOnHover: 'transition-all hover:-translate-y-1 hover:shadow-md duration-200',
  liftOnHoverLg: 'transition-all hover:-translate-y-2 hover:shadow-lg hover:shadow-primary/20 duration-300',
  glowOnHover: 'transition-all hover:shadow-lg hover:shadow-primary/25 duration-300',

  // Enhanced interactive animations
  pressDown: 'transition-transform active:scale-95 duration-75',
  gentleBounce: 'transition-transform hover:scale-102 active:scale-98 duration-150',
  magneticHover: 'transition-all hover:scale-105 hover:rotate-1 duration-200',

  // Loading animations
  spin: 'animate-spin',
  pulse: 'animate-pulse',
  bounce: 'animate-bounce',

  // Enhanced loading states
  shimmer: 'animate-pulse bg-gradient-to-r from-muted via-muted-foreground/10 to-muted bg-[length:200%_100%]',
  breathe: 'animate-pulse opacity-70 hover:opacity-100 transition-opacity duration-300',
} as const;

// Interactive state utilities
export const states = {
  // Focus states with proper accessibility
  focusRing: 'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2',
  focusVisible: 'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2',
  
  // Hover states
  hoverCard: 'hover:shadow-md hover:border-border/80 transition-all duration-200',
  hoverButton: 'hover:bg-primary/90 transition-colors duration-150',
  hoverGhost: 'hover:bg-accent hover:text-accent-foreground transition-colors duration-150',
  
  // Active states
  activeButton: 'active:scale-95 transition-transform duration-75',
  
  // Disabled states
  disabled: 'disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed',
} as const;

// Status color utilities
export const statusColors = {
  success: {
    bg: 'bg-green-50 dark:bg-green-950',
    border: 'border-green-200 dark:border-green-800',
    text: 'text-green-700 dark:text-green-300',
    icon: 'text-green-500',
  },
  warning: {
    bg: 'bg-yellow-50 dark:bg-yellow-950',
    border: 'border-yellow-200 dark:border-yellow-800',
    text: 'text-yellow-700 dark:text-yellow-300',
    icon: 'text-yellow-500',
  },
  error: {
    bg: 'bg-red-50 dark:bg-red-950',
    border: 'border-red-200 dark:border-red-800',
    text: 'text-red-700 dark:text-red-300',
    icon: 'text-red-500',
  },
  info: {
    bg: 'bg-blue-50 dark:bg-blue-950',
    border: 'border-blue-200 dark:border-blue-800',
    text: 'text-blue-700 dark:text-blue-300',
    icon: 'text-blue-500',
  },
} as const;

// Enhanced error state utilities with animated borders
export const errorStates = {
  // Basic error styling
  border: 'border-red-500 border-2',
  borderAnimated: 'border-red-500 border-2 animate-pulse',
  background: 'bg-red-50 dark:bg-red-950',
  text: 'text-red-700 dark:text-red-300',
  icon: 'text-red-500',

  // Error severity levels
  critical: {
    border: 'border-red-600 border-2',
    borderAnimated: 'border-red-600 border-2 animate-pulse',
    background: 'bg-red-100 dark:bg-red-900',
    text: 'text-red-800 dark:text-red-200',
    icon: 'text-red-600',
    shadow: 'shadow-red-200 dark:shadow-red-900',
  },
  warning: {
    border: 'border-orange-500 border-2',
    borderAnimated: 'border-orange-500 border-2 animate-pulse',
    background: 'bg-orange-50 dark:bg-orange-950',
    text: 'text-orange-700 dark:text-orange-300',
    icon: 'text-orange-500',
    shadow: 'shadow-orange-200 dark:shadow-orange-900',
  },
  info: {
    border: 'border-blue-500 border-2',
    borderAnimated: 'border-blue-500 border-2 animate-pulse',
    background: 'bg-blue-50 dark:bg-blue-950',
    text: 'text-blue-700 dark:text-blue-300',
    icon: 'text-blue-500',
    shadow: 'shadow-blue-200 dark:shadow-blue-900',
  },

  // Interactive states
  hover: 'hover:border-red-600 hover:shadow-lg hover:shadow-red-200 dark:hover:shadow-red-900',
  focus: 'focus:border-red-600 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',

  // Animation variants
  shake: 'animate-pulse hover:animate-none',
  glow: 'shadow-lg shadow-red-200 dark:shadow-red-900',

  // Form field errors
  input: cn(
    'border-red-500 border-2 bg-red-50 dark:bg-red-950',
    'focus:border-red-600 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
    'placeholder:text-red-400 dark:placeholder:text-red-500'
  ),

  // Button error states
  button: cn(
    'border-red-500 border-2 bg-red-500 text-white',
    'hover:bg-red-600 hover:border-red-600',
    'focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
    'disabled:bg-red-300 disabled:border-red-300'
  ),
} as const;

// Utility function to get status colors
export function getStatusColors(status: keyof typeof statusColors) {
  return statusColors[status];
}

// Responsive design utilities
export const responsive = {
  // Hide/show at breakpoints
  hideOnMobile: 'hidden sm:block',
  hideOnDesktop: 'block sm:hidden',
  showOnTablet: 'hidden md:block lg:hidden',
  
  // Grid responsive utilities
  mobileStack: 'flex flex-col sm:flex-row',
  tabletGrid: 'grid grid-cols-1 md:grid-cols-2',
  desktopGrid: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  
  // Text responsive utilities
  responsiveText: 'text-sm sm:text-base lg:text-lg',
  responsiveHeading: 'text-xl sm:text-2xl lg:text-3xl',
} as const;

// Form utilities
export const forms = {
  input: cn(
    'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2',
    'text-sm ring-offset-background placeholder:text-muted-foreground',
    states.focusVisible,
    states.disabled
  ),
  
  label: cn(typography.label, 'mb-2 block'),
  
  fieldset: 'space-y-4 p-4 border border-border rounded-lg',
  
  errorMessage: cn(typography.bodySmall, statusColors.error.text, 'mt-1'),
  
  helpText: cn(typography.caption, 'mt-1'),
} as const;

// Loading state utilities
export function getLoadingState(isLoading: boolean, content: string, loadingContent?: string) {
  if (isLoading) {
    return loadingContent || 'Loading...';
  }
  return content;
}

// Generate consistent spacing
export function generateSpacing(multiplier: number = 1) {
  return `${0.25 * multiplier}rem`;
}

// Accessibility utilities
export const accessibility = {
  // Screen reader only text
  srOnly: 'sr-only',
  
  // Skip links
  skipLink: cn(
    'absolute left-4 top-4 z-50 rounded-md bg-primary px-4 py-2 text-primary-foreground',
    'transform -translate-y-16 transition-transform focus:translate-y-0'
  ),
  
  // Landmark roles
  main: 'main',
  navigation: 'navigation', 
  banner: 'banner',
  contentinfo: 'contentinfo',
  
  // ARIA attributes helpers
  expanded: (isExpanded: boolean) => ({ 'aria-expanded': isExpanded }),
  selected: (isSelected: boolean) => ({ 'aria-selected': isSelected }),
  pressed: (isPressed: boolean) => ({ 'aria-pressed': isPressed }),
} as const;

// Enhanced visual design utilities
export const visualEffects = {
  // Modern shadows
  shadows: {
    soft: 'shadow-sm shadow-black/5',
    medium: 'shadow-md shadow-black/10',
    large: 'shadow-lg shadow-black/15',
    xl: 'shadow-xl shadow-black/20',
    colored: {
      primary: 'shadow-lg shadow-primary/20',
      success: 'shadow-lg shadow-green-500/20',
      warning: 'shadow-lg shadow-yellow-500/20',
      error: 'shadow-lg shadow-red-500/20',
    }
  },

  // Modern gradients
  gradients: {
    primary: 'bg-gradient-to-r from-primary to-primary/80',
    primaryVertical: 'bg-gradient-to-b from-primary to-primary/80',
    success: 'bg-gradient-to-r from-green-500 to-emerald-500',
    warning: 'bg-gradient-to-r from-yellow-500 to-orange-500',
    error: 'bg-gradient-to-r from-red-500 to-rose-500',
    subtle: 'bg-gradient-to-br from-muted/50 to-muted/20',
    glass: 'bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm',
    shimmer: 'bg-gradient-to-r from-transparent via-white/20 to-transparent',
  },

  // Glass morphism effects
  glass: {
    light: 'bg-white/10 backdrop-blur-sm border border-white/20',
    medium: 'bg-white/20 backdrop-blur-md border border-white/30',
    dark: 'bg-black/10 backdrop-blur-sm border border-black/20',
  },

  // Border effects
  borders: {
    gradient: 'border border-transparent bg-gradient-to-r from-primary/20 via-primary/10 to-primary/20 bg-clip-border',
    glow: 'border border-primary/30 shadow-[0_0_10px_rgba(59,130,246,0.3)]',
    animated: 'border-2 border-primary/50 animate-pulse',
  },

  // Modern card styles
  cards: {
    elevated: cn(
      'bg-card border border-border/50 rounded-lg',
      'shadow-lg shadow-black/5',
      'hover:shadow-xl hover:shadow-black/10',
      'transition-all duration-300'
    ),
    glass: cn(
      'bg-white/10 backdrop-blur-md border border-white/20 rounded-lg',
      'shadow-lg shadow-black/10',
      'hover:bg-white/15 transition-all duration-300'
    ),
    floating: cn(
      'bg-card border border-border/50 rounded-lg',
      'shadow-xl shadow-black/10',
      'hover:shadow-2xl hover:shadow-black/15 hover:-translate-y-1',
      'transition-all duration-300'
    ),
  }
} as const;

// Performance utilities
export const performance = {
  // Will-change for optimized animations
  willChange: 'will-change-transform',
  willChangeAuto: 'will-change-auto',

  // GPU acceleration
  gpu: 'transform-gpu',

  // Contain for performance
  contain: 'contain-layout contain-style',
} as const;