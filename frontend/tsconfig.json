{"compilerOptions": {"target": "es2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "incremental": true, "downlevelIteration": true, "baseUrl": ".", "paths": {"~/*": ["src/*"], "@/*": ["src/*"], "@test/*": ["src/__tests__/*"], "@components/*": ["src/components/*"], "@utils/*": ["src/utils/*"], "@panels/*": ["src/panels/*"], "@errors/*": ["src/errors/*"], "@types/*": ["src/types/*"], "@lighthouse/*": ["src/components/lighthouse/*"], "@lighthouse/core/*": ["src/components/lighthouse/core/*"], "@lighthouse/features/*": ["src/components/lighthouse/features/*"], "@lighthouse/shared/*": ["src/components/lighthouse/shared/*"], "@/icons/*": ["src/components/ui/icons/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"], "types": ["node"]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "src/types/**/*.ts"], "exclude": ["node_modules", "**/*.spec.ts", "**/cypress/**"]}