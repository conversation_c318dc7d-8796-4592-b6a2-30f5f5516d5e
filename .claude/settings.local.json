{"permissions": {"allow": ["Bash(npm run test:integration:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./restart-services.sh:*)", "mcp__ide__getDiagnostics", "Bash(npm run typecheck:*)", "Bash(npm run type-check:*)", "Bash(npm run dev:*)", "Bash(grep:*)", "Bash(ls:*)", "Bash(find:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(true)", "Bash(rm:*)", "Bash(npm run build:*)", "Bash(rg:*)", "<PERSON><PERSON>(docker-compose up:*)", "Bash(npx tsc:*)", "Bash(git checkout:*)", "Bash(timeout 8 npm run dev)", "<PERSON><PERSON>(timeout 5 npm run dev)", "<PERSON><PERSON>(curl:*)", "Bash(npm install)", "<PERSON><PERSON>(touch:*)", "Bash(npm run:*)", "<PERSON><PERSON>(sed:*)", "Bash(npx @tanstack/router-cli generate:*)", "Bash(npm install:*)", "<PERSON><PERSON>(npx playwright test:*)", "<PERSON><PERSON>(pkill:*)"], "deny": []}}